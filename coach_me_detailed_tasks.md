Coach Me - AI Agent Development Tasks (CTO-Level Detail)
🎯 Project Overview & Context
Product Vision: Transform spontaneous voice thoughts into structured, emotionally-grounded coaching prompts that drive behavioral change through self-awareness and scheduled reinforcement.
Business Context: This is a minimum viable product (MVP) targeting the $4.4B personal development app market, with unique differentiation through voice-first interaction and emotional motivation capture.
Technical Philosophy: Domain-driven design with event-sourcing, clean architecture, and maximum testability. Every component must be independently deployable and scalable to 100k+ users.

PHASE 1: Voice Recording Infrastructure Foundation 🎤
📋 Phase 1 Overview
Objective: Establish bulletproof voice recording infrastructure with production-grade error handling, accessibility, and performance optimization.
Success Metrics:

<500ms UI response time


95% browser compatibility


Zero audio dropouts during recording
Graceful degradation on unsupported browsers


Task 1.1: Project Architecture & Setup
Priority: P0 (Blocking) | Estimated Time: 4-6 hours
Context & Requirements
You're building the foundation of a voice-first application that will scale to handle thousands of concurrent voice recordings. The architecture must support real-time audio processing, multi-language support, and future AI integration.
Detailed Instructions
Step 1.1.1: Initialize Next.js Project

Use Next.js 14 with App Router (not Pages Router) for better performance and developer experience
Enable TypeScript from the start - this is non-negotiable for maintainability
Configure Tailwind CSS for rapid UI development with consistent design system
Set up ESLint with strict rules to catch errors early
Use src directory structure for better organization
Configure import aliases (@/*) to avoid relative import hell

Step 1.1.2: Install Core Dependencies
You need these specific packages:

zustand: For lightweight, type-safe state management (avoid Redux complexity)
zod: For runtime type validation and API contract enforcement
clsx: For conditional CSS class management
lucide-react: For consistent, lightweight icons
Ensure all packages are latest stable versions to avoid security vulnerabilities

Step 1.1.3: Project Structure Setup
Create a scalable folder structure that follows domain-driven design principles:

components/ui/ - Only pure, reusable UI components with no business logic
lib/ - Pure utility functions, types, and constants
hooks/ - Custom React hooks for reusable stateful logic
stores/ - Zustand stores for global state management
__tests__/ - Co-located tests following the same structure as src

Step 1.1.4: Configuration Files

tailwind.config.js: Extend default theme with brand colors, custom animations for voice feedback
next.config.js: Enable experimental features like typed routes, optimize images, remove console logs in production
tsconfig.json: Strict TypeScript settings with path mapping
.eslintrc.json: Extend Next.js ESLint config with additional rules for code quality
prettier.config.js: Consistent code formatting with Tailwind class sorting

Acceptance Criteria

 Project initializes without errors or warnings
 All dependencies install with compatible versions (no peer dependency conflicts)
 TypeScript compilation works without any errors
 Tailwind CSS compiles and hot-reloads properly
 Development server starts on localhost:3000 and displays default page
 ESLint shows no errors on initial codebase
 Prettier formats code correctly with Tailwind class ordering

Quality Gates

Run npm run build - must complete without errors
Run npm run lint - must show zero errors
Check bundle size - initial bundle should be <200KB
Verify hot reload works in under 200ms


Task 1.2: Core Type Definitions & Domain Models
Priority: P0 (Blocking) | Estimated Time: 3-4 hours
Context & Requirements
Establish bulletproof TypeScript contracts that will serve as the single source of truth for all audio operations. These types must be designed for extension - they'll support multi-language processing, AI integration, and future coaching features. Think of these as your API contracts that will never break.
Detailed Instructions
Step 1.2.1: Core Domain Types (src/lib/types.ts)
Define the fundamental domain objects that represent your business logic:
AudioRecording Interface Design:

id: Use UUIDs, not auto-incrementing numbers (better for distributed systems)
transcript: The raw text from speech recognition
confidence: Float 0-1 representing speech recognition confidence (critical for UX decisions)
audioBlob: Nullable because we might process without storing audio
duration: Milliseconds (not seconds) for precision
language: Enum type for supported languages (en-US, he-IL initially)
timestamp: Use Date objects (JSON serializable)
metadata: Separate object for recording context (device, browser, quality metrics)

RecordingMetadata Design Principles:

Capture device type for different UX flows (mobile vs desktop)
Browser type for troubleshooting compatibility issues
Audio quality metrics for debugging poor transcription
Technical specs (sample rate, channels) for audio processing optimization

Step 1.2.2: State Management Types

RecordingState: Represents the current state of the recording process
Use discriminated unions for RecordingStatus (TypeScript will enforce valid state transitions)
AudioError: Structured error handling with error codes, user messages, and recovery flags
Include confidence scores and audio levels for real-time UI feedback

Step 1.2.3: Error Handling Strategy
Design a robust error system that handles all failure modes:
Error Code Categories:

Permission errors (PERMISSION_DENIED, DEVICE_NOT_FOUND)
Technical errors (RECORDING_FAILED, TRANSCRIPTION_FAILED)
Network errors (NETWORK_ERROR)
Compatibility errors (UNSUPPORTED_BROWSER)

Error Object Design:

code: Enum for programmatic handling
message: User-friendly message for display
details: Technical details for debugging (don't show to users)
recoverable: Boolean flag to determine if user can retry

Step 1.2.4: Configuration Types & Constants (src/lib/constants.ts)
Define all configuration objects as immutable constants:

Audio processing settings (sample rate, noise cancellation)
Recording limits (max duration, file size)
Language configuration with RTL support flags
Browser compatibility matrix
Error message translations for both English and Hebrew

Acceptance Criteria

 All types compile without TypeScript errors
 Types support both English and Hebrew languages
 Error types cover all possible failure scenarios
 Configuration types are immutable and type-safe
 Types are exported correctly and can be imported in other files
 JSDoc comments explain complex types
 Types follow consistent naming conventions (PascalCase for interfaces, camelCase for properties)

Quality Gates

Create a test file that imports all types - must compile
Types should enable autocomplete in VSCode
No any types allowed (use unknown if truly dynamic)
All types should be serializable to JSON


Task 1.3: Browser Compatibility & Feature Detection
Priority: P0 (Blocking) | Estimated Time: 2-3 hours
Context & Requirements
Voice recording depends on browser APIs that have varying support and behavior. You must detect capabilities early and provide graceful fallbacks. This system will determine if users can use the app at all.
Detailed Instructions
Step 1.3.1: Feature Detection Utility (src/lib/browserCompat.ts)
Create a comprehensive browser capability detection system:
Core Capabilities to Check:

navigator.mediaDevices.getUserMedia - Modern media access
webkitSpeechRecognition or SpeechRecognition - Speech recognition API
MediaRecorder - Audio recording capability
AudioContext - Audio processing
Blob and URL.createObjectURL - File handling

Browser-Specific Checks:

Safari: Different permission model, requires user gesture for microphone access
Firefox: Limited speech recognition support
Chrome: Full feature support but may have different audio formats
Mobile browsers: Touch event requirements, different audio constraints

Step 1.3.2: Capability Assessment Function
Design a function that returns a capability profile:

Overall compatibility score (0-100)
List of supported features
List of missing features with fallback suggestions
Performance warnings (e.g., "Safari on iOS has limited background processing")

Step 1.3.3: Graceful Degradation Strategy
Define fallback behaviors for each missing capability:

No speech recognition: Show text input alternative
No audio recording: Allow text-only mode
No microphone access: Clear instructions for enabling permissions
Old browser: Show upgrade message with download links

Step 1.3.4: Permission Handling System
Create robust permission request flow:

Check current permission state before requesting
Handle all permission states (granted, denied, prompt)
Provide clear instructions for re-enabling permissions
Different flows for different browsers (Safari requires different approach)
Store permission state to avoid repeated prompts

Acceptance Criteria

 Function correctly identifies browser type and version
 All required Web APIs are properly detected
 Permission states are correctly identified
 Fallback recommendations are provided for each missing feature
 Function works on iOS Safari, Chrome Mobile, Chrome Desktop, Firefox
 Returns consistent results across browser sessions
 Handles edge cases like incognito mode or restricted environments

Quality Gates

Test on minimum 5 different browser/OS combinations
Function should never throw exceptions (only return capability data)
Performance: Feature detection should complete in <100ms
Results should be cached to avoid repeated capability checks


Task 1.4: Audio Recording Hook Implementation
Priority: P0 (Blocking) | Estimated Time: 6-8 hours
Context & Requirements
This is the core of your voice recording system. The hook must handle the complex state machine of audio recording: permission requests, device initialization, recording state management, error handling, and cleanup. This hook will be reused across the entire application.
Detailed Instructions
Step 1.4.1: Hook Architecture Design (src/hooks/useAudioRecording.ts)
Design a custom hook that encapsulates all recording logic:
State Management Strategy:

Use useReducer instead of multiple useState calls for complex state
State should include: recording status, transcript, audio blob, error state, duration, audio levels
Implement proper state transitions (idle → requesting permission → recording → processing → completed)
Add state validation to prevent invalid transitions

Step 1.4.2: MediaDevices Integration
Handle the Web Audio API integration:
getUserMedia Configuration:

Request audio with specific constraints (echo cancellation, noise suppression)
Handle different browser implementations of audio constraints
Implement retry logic with different constraint sets if first attempt fails
Store MediaStream reference for proper cleanup

MediaRecorder Setup:

Choose optimal audio format based on browser support (webm, mp4, wav)
Configure recording options (bitrate, sample rate)
Handle dataavailable events to collect audio chunks
Implement proper start/stop/pause functionality

Step 1.4.3: Speech Recognition Integration
Implement real-time transcription:
SpeechRecognition Configuration:

Use webkitSpeechRecognition with fallback to SpeechRecognition
Configure for continuous recognition with interim results
Set language based on user preference or browser locale
Handle recognition errors and restart logic

Real-time Transcript Processing:

Update transcript as interim results come in
Finalize transcript on result events
Handle multiple recognition languages
Store confidence scores for quality assessment

Step 1.4.4: Error Handling & Recovery
Implement comprehensive error management:
Error Categories:

Permission errors: Clear instructions for user action
Device errors: Retry with different audio constraints
Network errors: Offline mode or retry logic
Browser compatibility: Graceful degradation

Recovery Strategies:

Automatic retry for transient errors
User-initiated retry for permission issues
Clear error messages with actionable instructions
Cleanup resources on any error

Step 1.4.5: Performance Optimization
Ensure the hook performs well under load:
Memory Management:

Properly dispose of MediaStream objects
Clean up event listeners
Release audio blobs when no longer needed
Implement cleanup in useEffect return function

Audio Level Monitoring:

Use AudioContext and AnalyserNode for real-time volume detection
Implement efficient audio level calculation (not every frame)
Provide audio levels for UI visualization
Handle audio processing in separate thread if possible

Hook API Design
The hook should return an object with:

State: { isRecording, transcript, error, duration, audioLevel, status }
Actions: { startRecording, stopRecording, clearError, reset }
Data: { audioBlob, finalTranscript, confidence }

Acceptance Criteria

 Hook handles all recording states correctly
 Real-time transcription works in both English and Hebrew
 Audio levels are provided for UI visualization
 All error scenarios are handled gracefully
 Resources are properly cleaned up on unmount
 Hook works on mobile and desktop browsers
 Recording can be started/stopped multiple times without issues
 Audio quality is suitable for speech recognition

Quality Gates

No memory leaks after multiple record/stop cycles
Recording starts within 500ms of user action
Transcription accuracy >80% for clear speech
Hook should work offline (no transcription but audio recording works)
Error states should recover automatically when possible


Task 1.5: Voice Input UI Component
Priority: P1 (High) | Estimated Time: 4-5 hours
Context & Requirements
Create a beautiful, accessible voice input component that provides clear visual feedback for all recording states. This component must work flawlessly on mobile devices and provide an intuitive user experience that encourages voice interaction.
Detailed Instructions
Step 1.5.1: Component Architecture (src/components/VoiceInput.tsx)
Design a pure, reusable component:
Props Interface:

onRecordingComplete: Callback with AudioRecording object
onError: Error callback with structured error information
language: Optional language override
maxDuration: Optional recording time limit
disabled: For form integration
className: For styling flexibility

Component State Strategy:

Use the custom hook from Task 1.4 for all recording logic
Component state should only manage UI-specific concerns (animations, visual feedback)
No business logic in the component - it's purely presentational

Step 1.5.2: Visual Design System
Create engaging visual feedback:
Recording Button States:

Idle: Large, prominent microphone icon with subtle shadow
Recording: Pulsing animation with red indicator
Processing: Spinner with "processing" message
Error: Red state with error icon
Completed: Green checkmark with success state

Animation Strategy:

Use CSS transforms (not JavaScript animations) for performance
Implement smooth transitions between states
Add audio level visualization (waveform or circular progress)
Ensure animations work smoothly on mobile devices

Step 1.5.3: Accessibility Implementation
Make the component fully accessible:
ARIA Labels:

Proper button labeling for screen readers
Live region announcements for state changes
Role attributes for complex UI elements

Keyboard Navigation:

Space bar to start/stop recording
Enter key as alternative trigger
Escape key to cancel recording
Tab navigation support

Visual Accessibility:

High contrast colors for all states
Large touch targets (minimum 44px)
Clear visual feedback for all interactions
Support for reduced motion preferences

Step 1.5.4: Mobile Optimization
Ensure excellent mobile experience:
Touch Interactions:

Large, easy-to-tap button (minimum 60px on mobile)
Prevent double-tap zoom on recording button
Handle touch events properly (touchstart, touchend)
Provide haptic feedback where available

Mobile-Specific Features:

Handle device orientation changes
Optimize for different screen sizes
Handle mobile browser quirks (Safari audio restrictions)
Battery usage optimization

Step 1.5.5: Error State Management
Implement comprehensive error UI:
Error Display Strategy:

Non-intrusive error messages (toast or inline)
Clear, actionable error messages
Retry buttons for recoverable errors
Help links for complex issues (permission setup)

Error Recovery Flow:

Automatic retry for transient errors
User-guided recovery for permission issues
Clear instructions for browser-specific problems
Fallback to text input when voice fails

Acceptance Criteria

 Component renders correctly in all recording states
 Animations are smooth and performant on mobile
 All accessibility requirements are met (WCAG 2.1 AA)
 Touch interactions work properly on iOS and Android
 Error states provide clear guidance to users
 Component is fully responsive across screen sizes
 Voice feedback works in both English and Hebrew
 Component can be used multiple times on the same page

Quality Gates

Test on iOS Safari, Chrome Mobile, Chrome Desktop, Firefox
Lighthouse accessibility score >95
No layout shift during state transitions
Touch targets meet minimum size requirements
Component works with keyboard navigation only


Task 1.6: Audio Visualization Component
Priority: P2 (Medium) | Estimated Time: 3-4 hours
Context & Requirements
Provide real-time visual feedback during recording to enhance user confidence and engagement. The visualization should be lightweight, accessible, and work smoothly across all devices.
Detailed Instructions
Step 1.6.1: Visualization Architecture (src/components/AudioVisualizer.tsx)
Create a performant audio visualization system:
Visualization Types:

Circular progress indicator showing audio levels
Simple waveform display (not complex frequency analysis)
Pulsing animation that responds to voice amplitude
Minimalist design that doesn't distract from main UI

Performance Requirements:

Use CSS animations with transforms for smooth performance
Limit re-renders to essential updates only
Use requestAnimationFrame for smooth animation loops
Implement efficient audio level calculation

Step 1.6.2: Audio Level Processing
Implement efficient audio analysis:
AudioContext Integration:

Connect to MediaStream from recording hook
Use AnalyserNode for frequency analysis
Calculate RMS (Root Mean Square) for volume levels
Implement smoothing to prevent jittery animations

Performance Optimization:

Update visualization at 30fps maximum (not 60fps)
Use efficient Uint8Array processing
Debounce rapid level changes
Clean up audio processing when component unmounts

Step 1.6.3: Visual Design Implementation
Create appealing visual feedback:
Design Patterns:

Subtle color transitions based on audio levels
Smooth scaling animations for volume indication
Consistent with overall app design language
Support for both light and dark themes

Animation Smoothing:

Use cubic-bezier easing functions
Implement attack and decay curves for natural feel
Prevent abrupt visual changes
Maintain visual consistency across browsers

Step 1.6.4: Accessibility Considerations
Ensure visualization is accessible:
Motion Sensitivity:

Respect prefers-reduced-motion settings
Provide static alternative for motion-sensitive users
Implement toggle to disable animations
Ensure core functionality works without visualization

Screen Reader Support:

Provide audio level information via aria-live regions
Use descriptive text for visualization state
Don't rely solely on visual feedback

Acceptance Criteria

 Visualization responds smoothly to audio input
 Performance remains smooth on low-end mobile devices
 Respects accessibility preferences (reduced motion)
 Integrates seamlessly with VoiceInput component
 Works without audio visualization if disabled
 Visual design is consistent with app theme
 No memory leaks from audio processing

Quality Gates

Maintains 30fps during recording on mobile devices
CPU usage <10% during visualization
Visualization starts within 100ms of recording start
No visual glitches during state transitions


Task 1.7: Main Page Integration & Layout
Priority: P1 (High) | Estimated Time: 2-3 hours
Context & Requirements
Create the main application page that showcases the voice recording functionality. This page serves as the primary user interface and must provide a clean, focused experience that encourages voice interaction.
Detailed Instructions
Step 1.7.1: Main Page Layout (src/app/page.tsx)
Design a clean, focused interface:
Layout Strategy:

Single-column layout optimized for mobile-first design
Prominent voice input component as the primary call-to-action
Minimal distractions - focus entirely on voice recording
Responsive design that works from 320px to 2560px widths

Content Hierarchy:

App title/logo at top (small, not prominent)
Brief instruction text ("Tap to speak your thought")
Large voice input component (center of screen)
Recording status/feedback area
Optional help/about section (collapsible)

Step 1.7.2: State Management Integration
Integrate with recording system:
Recording Flow:

Handle recording completion events
Display transcript results clearly
Provide options to re-record or continue
Show recording history (simple list)

Error Handling:

Display error messages in context
Provide recovery actions
Show browser compatibility warnings if needed
Guide users through permission setup

Step 1.7.3: Responsive Design Implementation
Ensure excellent experience across devices:
Mobile Optimization:

Touch-friendly button sizes
Proper viewport configuration
Handle virtual keyboard appearance
Optimize for portrait and landscape orientations

Desktop Experience:

Keyboard shortcuts (spacebar to record)
Proper focus management
Mouse hover states
Accessible tab navigation

Step 1.7.4: Loading States & Performance
Implement smooth loading experience:
Initial Load:

Fast initial page render (<1s on 3G)
Progressive enhancement for audio features
Graceful degradation for unsupported browsers
Show loading states for async operations

Runtime Performance:

Minimize re-renders during recording
Efficient state updates
Proper cleanup of resources
Optimize bundle size

Acceptance Criteria

 Page loads quickly on slow connections
 Voice recording works seamlessly from main page
 Layout is responsive across all screen sizes
 All interactive elements are keyboard accessible
 Error states are handled gracefully
 Page works without JavaScript (basic functionality)
 Recording transcripts are displayed clearly

Quality Gates

Lighthouse Performance score >90
First Contentful Paint <1.5s on 3G
Cumulative Layout Shift <0.1
Works on iOS Safari, Chrome Mobile, Chrome Desktop


Task 1.8: Testing Implementation
Priority: P1 (High) | Estimated Time: 4-5 hours
Context & Requirements
Implement comprehensive testing to ensure reliability and catch regressions early. Focus on testing the critical voice recording functionality and user interactions.
Detailed Instructions
Step 1.8.1: Test Setup Configuration
Configure testing environment:
Jest Configuration:

Set up Jest with jsdom environment for browser API mocking
Configure module path mapping to match tsconfig paths
Set up test coverage reporting with minimum thresholds
Configure test timeout for async operations

Testing Library Setup:

Configure React Testing Library with custom render function
Set up global test utilities and matchers
Configure cleanup and teardown procedures
Set up test data factories and fixtures

Step 1.8.2: Unit Tests for Core Functions
Test all utility functions and business logic:
Browser Compatibility Tests:

Test feature detection functions
Mock different browser environments
Test capability assessment logic
Verify error handling for unsupported browsers

Type Validation Tests:

Test all TypeScript interfaces with invalid data
Verify error type structures
Test configuration object validation
Ensure type safety boundaries

Step 1.8.3: Hook Testing
Test the audio recording hook thoroughly:
State Management Tests:

Test all state transitions
Verify error states are handled correctly
Test cleanup on unmount
Mock Web APIs (getUserMedia, SpeechRecognition)

Error Handling Tests:

Test permission denied scenarios
Test device not found errors
Test network errors during transcription
Verify recovery mechanisms work

Step 1.8.4: Component Testing
Test UI components with user interactions:
VoiceInput Component Tests:

Test button click interactions
Test keyboard navigation
Test error state rendering
Mock audio recording hook responses

Accessibility Tests:

Test screen reader compatibility
Verify ARIA labels and roles
Test keyboard navigation flows
Check color contrast and visual design

Step 1.8.5: Integration Tests
Test complete user flows:
Recording Flow Tests:

Test complete record → transcribe → display flow
Test multiple recording sessions
Test error recovery flows
Test browser compatibility scenarios

Performance Tests:

Test memory usage during extended recording
Verify cleanup of resources
Test performance on slow devices (simulated)
Check for memory leaks

Acceptance Criteria

 All unit tests pass with >90% code coverage
 All component tests pass with user interaction scenarios
 Integration tests cover critical user flows
 Tests can be run in CI/CD environment
 Performance tests validate memory usage
 Accessibility tests ensure WCAG compliance
 Mock implementations cover all external dependencies

Quality Gates

Test suite completes in <30 seconds
Code coverage >90% for core business logic
All tests pass consistently (no flaky tests)
Tests can run offline (no external dependencies)


Task 1.9: Documentation & Developer Experience
Priority: P2 (Medium) | Estimated Time: 2-3 hours
Context & Requirements
Create comprehensive documentation to enable easy maintenance and future development. Documentation should serve both current development and future team members.
Detailed Instructions
Step 1.9.1: API Documentation
Document all public interfaces:
Component Documentation:

Document all component props with examples
Include usage examples for common scenarios
Document accessibility features
Provide troubleshooting guide for common issues

Hook Documentation:

Document hook parameters and return values
Provide usage examples with error handling
Document performance considerations
Include integration examples

Step 1.9.2: Architecture Documentation
Document system design decisions:
Technical Architecture:

Document overall system architecture
Explain state management strategy
Document error handling patterns
Describe performance optimization techniques

Browser Compatibility:

Document supported browsers and versions
List known limitations and workarounds
Provide fallback strategies
Include testing matrix

Step 1.9.3: Development Setup Guide
Create comprehensive setup instructions:
Quick Start Guide:

Step-by-step development environment setup
Required tools and versions
Common troubleshooting steps
Local testing procedures

Contributing Guidelines:

Code style and formatting requirements
Testing requirements for new features
Pull request process
Release procedures

Step 1.9.4: Deployment Documentation
Document deployment procedures:
Build Process:

Production build configuration
Environment variable requirements
Performance optimization steps
Bundle analysis procedures

Monitoring & Debugging:

Error tracking setup
Performance monitoring
User analytics integration
Debugging common issues

Acceptance Criteria

 All public APIs are documented with examples
 Architecture decisions are clearly explained
 Setup guide allows new developers to start quickly
 Troubleshooting guides address common issues
 Documentation is kept up-to-date with code changes
 Examples work and can be copy-pasted

Quality Gates

New team member can set up project in <30 minutes
Documentation covers all major features
Examples in documentation actually work
Architecture decisions are clearly justified


PHASE 1 COMPLETION CHECKLIST
Final Integration Testing

 Complete user flow works: record → transcribe → display
 Performance meets requirements (<500ms response time)
 Works on target browsers (iOS Safari, Chrome Mobile, Chrome Desktop, Firefox)
 Accessibility requirements met (WCAG 2.1 AA)
 Error handling works for all failure scenarios
 Memory usage is acceptable (no leaks)
 Audio quality is suitable for speech recognition

Production Readiness

 Build process completes without errors
 Bundle size is optimized (<200KB initial load)
 Security headers are configured
 Error monitoring is set up
 Performance monitoring is configured
 Basic analytics are implemented

Documentation Complete

 README with quick start guide
 API documentation for all components
 Architecture decision records (ADRs)
 Troubleshooting guide
 Browser compatibility matrix
 Performance benchmarks documented

Handoff to Phase 2

 Voice recording system is stable and tested
 Architecture supports AI integration
 Error handling is comprehensive
 Performance baselines are established
 Code quality standards are met
 Team knowledge transfer is complete


SUCCESS METRICS FOR PHASE 1
Technical Metrics:

Voice recording success rate: >95%
Average transcription accuracy: >80%
Time to start recording: <500ms
Browser compatibility: >95% of target browsers
Bundle size: <200KB compressed

User Experience Metrics:

Task completion rate: >90% for basic recording
Error recovery rate: >80% for permission issues
Accessibility compliance: WCAG 2.1 AA
Mobile performance: Smooth on low-end devices

Code Quality Metrics:

Test coverage: >90%
TypeScript strict mode: 100% compliance
ESLint errors: 0
Performance budget: Met
Security vulnerabilities: 0 high/critical


PHASE 2: AI Prompt Refinement Engine 🧠🗣️
📋 Phase 2 Overview
Objective: Build the core pipeline that transforms raw transcribed voice input into structured, emotionally-informed coaching prompts, enriched with scheduling metadata and follow-up questions. This includes integrating AI models, motivation capture, and time expression analysis.

Success Metrics:

90% of inputs converted into structured prompts

<5s AI response latency

Motivation captured in >80% of prompts

Time expressions extracted with >90% accuracy

Task 2.1: Transcription Pipeline Integration
Priority: P0 (Blocking) | Estimated Time: 4–6 hours

Context & Requirements
Phase 1 gave us the audio recording. Now we must convert that to text reliably. We’re building a transcription pipeline that works both in real-time (when browser supports it) and via backend fallback (Whisper or other models).

Detailed Instructions
Step 2.1.1: Interface Design

Input: AudioRecording object

Output: TranscriptionResult

ts
Copy
Edit
interface TranscriptionResult {
  transcript: string;
  confidence: number;
  language: 'en-US' | 'he-IL';
}
Step 2.1.2: Browser SpeechRecognition (Fast Path)

Use webkitSpeechRecognition if available

Return interim + final transcript

Auto-detect language based on UI setting

Emit confidence score (if available)

Step 2.1.3: Whisper Integration (Fallback Path)

Send audioBlob to backend via /api/transcribe

Backend calls local Whisper instance (or cloud provider)

Return transcript + confidence estimate + language

Cache result on server (avoid duplicate computation)

Step 2.1.4: Error Handling

Fallback to Whisper if browser fails

Show retry option for permanent failure

Return structured TranscriptionError object with:

type: 'permission' | 'audio' | 'network' | 'unrecognized'

message: user-facing error

recoverable: boolean

Acceptance Criteria
 Works across desktop/mobile browsers

 Whisper fallback works with large audio blobs (>30s)

 Transcription accuracy ≥ 80%

 Confidence scores are returned and usable

 Error scenarios handled gracefully

 Full coverage with unit tests

Task 2.2: Motivation Capture Flow
Priority: P0 | Estimated Time: 4 hours

Context & Requirements
This is the emotional backbone of Coach Me. After the first transcription, the user is asked why they want it — this step increases engagement and makes the AI's prompt more accurate and human.

Detailed Instructions
Step 2.2.1: Follow-Up UI

Immediately after initial transcript, display voice/text input asking:
"Why is this important to you?"

Accept voice input OR short text

Display real-time transcription for voice input

Auto-trim and store response in MotivationResponse:

ts
Copy
Edit
interface MotivationResponse {
  text: string;
  method: 'voice' | 'text';
  timestamp: Date;
}
Step 2.2.2: Validation

Require non-empty response OR allow skip with flag skippedMotivation: true

Store motivation alongside original transcript

Step 2.2.3: UX Enhancements

Allow retry or edit motivation before submission

Animate into final confirmation view

Acceptance Criteria
 Voice and text input supported

 User can review/edit response

 Skipped motivation handled explicitly

 Component works in light and dark mode

 Stored with metadata and linked to prompt

Task 2.3: AI Prompt Refinement Pipeline
Priority: P0 | Estimated Time: 6–8 hours

Context & Requirements
This is the transformation engine. We send the transcript + motivation to an AI model (e.g., Claude, GPT) and receive a structured coaching prompt ready for agent execution.

Detailed Instructions
Step 2.3.1: Prompt Template Construction
Send the following payload to the AI model:

ts
Copy
Edit
interface PromptRefinementRequest {
  originalText: string; // from transcription
  motivation: string; // from follow-up
  language: 'he-IL' | 'en-US';
}
Prompt Content Template:

less
Copy
Edit
You are a coaching assistant. A user has spoken a request.

Your job:
1. Refine what they said into a clear, structured coaching instruction.
2. Extract time expressions (e.g., “every morning”) and format them.
3. Use the motivation to personalize the prompt.
4. If time is vague, return a follow-up question.

Inputs:
Original: {originalText}
Motivation: {motivation}
Language: {language}

Respond with:
- agentPrompt
- time (formatted)
- followUpQuestion (if needed)
Step 2.3.2: Claude API Integration

Use streaming or JSON endpoint (Anthropic or OpenAI)

Send structured request with retry logic

Store full prompt exchange for auditing

Enforce timeout (5s max wait)

Step 2.3.3: Response Schema

ts
Copy
Edit
interface RefinedPromptResult {
  agentPrompt: string;
  time: string; // e.g. "every day at 08:00"
  followUpQuestion?: string;
}
Step 2.3.4: Language-Specific Behavior

Keep response in same language as original input

Do NOT translate content

Clarify ambiguous time in user's own language

Acceptance Criteria
 API responds in <5s

 Refined prompt includes motivation

 Time is structured and usable

 Follow-up question returned for vague inputs

 Supports Hebrew and English

Task 2.4: Prompt Review & Confirmation UI
Priority: P1 | Estimated Time: 4–5 hours

Context & Requirements
After AI processing, the user needs to confirm the final result — this is their last chance to review before the system starts coaching them on this goal.

Detailed Instructions
Step 2.4.1: Review Card UI

Show:

Refined coaching prompt

Extracted schedule (in local time)

Motivation summary

Follow-up question if returned

Clean, minimal layout with large text and calming design

Step 2.4.2: Interaction Options

Confirm → saves prompt + time to DB

Edit → lets user modify prompt (text)

Answer follow-up → reprocesses with new time

Retry AI → resubmit if prompt seems wrong

Step 2.4.3: Storage Model

ts
Copy
Edit
interface FinalizedPrompt {
  id: string;
  refined: RefinedPromptResult;
  motivation: string;
  schedule: { type: 'daily' | 'weekly'; time: string };
  confirmedAt: Date;
}
Acceptance Criteria
 User can confirm or revise

 Follow-up response triggers re-run

 Prompt is saved to database

 Review screen is accessible and works across devices

Task 2.5: Prompt Scheduling Framework
Priority: P1 | Estimated Time: 5 hours

Context & Requirements
Now that we have structured, confirmed prompts — we need to schedule agent actions (e.g., daily reminders, weekly nudges).

Detailed Instructions
Step 2.5.1: Prompt Scheduler Design

For each confirmed prompt, create a scheduled job:

type: cron or interval

Action: send agent message to user

Store schedule metadata in ScheduledPrompt

Step 2.5.2: Trigger Engine

At scheduled time:

Load prompt

Send push notification or webhook

Log delivery result

Step 2.5.3: Time Zone & Local Time Handling

Store times in UTC

Display local time in UI

Support time zone switching later

Acceptance Criteria
 Scheduled prompt stored in DB

 Notification triggered at correct local time

 Prompt history visible to user

 Retry logic on failed delivery

PHASE 2 Completion Checklist
 Transcription pipeline works with Whisper fallback

 Motivation capture is smooth and reflects user intent

 AI refinement generates clear, structured prompts

 Time expressions are extracted or clarified

 Final result review UI is responsive and clear

 Scheduling framework triggers prompts reliably

Success Metrics – Phase 2
Technical

AI latency: <5s

Motivation capture: >80% of sessions

Time resolution accuracy: >90%

Prompt confirmation rate: >85%

UX

90% task completion

<5% rejections of refined prompt

95%+ follow-up question clarity

Scalability

1k+ refined prompts/day with <200ms DB latency

AI API timeout retries under 0.1% failure rate

