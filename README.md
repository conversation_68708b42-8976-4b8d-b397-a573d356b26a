# Coach Me - Voice-First Personal Development

Transform your spontaneous voice thoughts into structured, emotionally-grounded coaching prompts that drive behavioral change through self-awareness and scheduled reinforcement.

## 🎯 Project Overview

Coach Me is a minimum viable product (MVP) targeting the $4.4B personal development app market, with unique differentiation through voice-first interaction and emotional motivation capture.

### Technical Philosophy
- **Domain-driven design** with event-sourcing
- **Clean architecture** with maximum testability
- **Performance-first** - every component independently deployable
- **Scalable** to 100k+ users

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Modern browser with microphone access

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd coachme

# Install dependencies
npm install

# Start development server
npm run dev

# Open http://localhost:3000
```

### Development Commands

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run test         # Run test suite
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage report
```

## 🏗️ Architecture

### Project Structure

```
src/
├── app/                 # Next.js App Router pages
│   ├── layout.tsx      # Root layout with metadata
│   └── page.tsx        # Main application page
├── components/         # React components
│   ├── ui/            # Pure UI components
│   ├── VoiceInput.tsx # Main voice recording component
│   └── AudioVisualizer.tsx # Audio level visualization
├── hooks/             # Custom React hooks
│   └── useAudioRecording.ts # Core recording logic
├── lib/               # Utilities and business logic
│   ├── types.ts       # TypeScript type definitions
│   ├── constants.ts   # Application constants
│   └── browserCompat.ts # Browser compatibility utilities
├── stores/            # Zustand state management
└── __tests__/         # Test files
```

### Core Components

#### VoiceInput Component
The main voice recording interface with:
- Real-time speech recognition
- Audio level visualization
- Error handling and recovery
- Accessibility support
- Mobile optimization

#### useAudioRecording Hook
Core recording logic handling:
- MediaDevices API integration
- Speech recognition processing
- State management with useReducer
- Error handling and cleanup
- Performance optimization

#### Browser Compatibility System
Comprehensive feature detection for:
- MediaDevices support
- Speech Recognition API
- Audio processing capabilities
- Permission management
- Graceful degradation

## 🎨 Design System

### Brand Colors
```css
--primary: #3b82f6      /* Blue - trust, communication */
--secondary: #10b981    /* Green - growth, success */
--accent: #f59e0b       /* Amber - energy, motivation */
--voice: #8b5cf6        /* Purple - voice, creativity */
```

### Animations
- **pulse-voice**: Breathing animation for recording state
- **recording**: Expanding circle for active recording
- **audio-wave**: Waveform visualization
- **voice-feedback**: Smooth state transitions

## 🧪 Testing

### Test Coverage Requirements
- **Global**: 80% minimum coverage
- **Core Libraries** (`src/lib/`): 90% minimum
- **Hooks** (`src/hooks/`): 85% minimum

### Running Tests

```bash
# Run all tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- VoiceInput.test.tsx

# Run tests in watch mode
npm run test:watch
```

### Test Structure
- **Unit Tests**: Core functions and utilities
- **Component Tests**: UI components with user interactions
- **Integration Tests**: Complete user flows
- **Accessibility Tests**: WCAG 2.1 AA compliance

## 🌐 Browser Support

### Supported Browsers
- **Chrome**: 91+ (full support)
- **Firefox**: 89+ (limited speech recognition)
- **Safari**: 14+ (requires user gesture for microphone)
- **Edge**: 91+ (full support)

### Required Features
- MediaDevices API (getUserMedia)
- MediaRecorder API
- AudioContext API
- Blob and URL APIs
- Speech Recognition API (optional)

### Graceful Degradation
- No speech recognition → Text input fallback
- No audio recording → Text-only mode
- No microphone access → Clear setup instructions
- Old browser → Upgrade recommendations

## 🔧 Configuration

### Environment Variables
```bash
# Optional: Analytics and monitoring
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id
NEXT_PUBLIC_ERROR_TRACKING_DSN=your-sentry-dsn

# Optional: Feature flags
NEXT_PUBLIC_ENABLE_OFFLINE_MODE=true
NEXT_PUBLIC_MAX_RECORDING_DURATION=300000
```

### Build Configuration
- **Bundle size limit**: 200KB initial load
- **Performance budget**: Lighthouse score >90
- **Accessibility**: WCAG 2.1 AA compliance
- **Security**: CSP headers, XSS protection

## 📱 Mobile Optimization

### Touch Interactions
- Minimum 44px touch targets
- Haptic feedback where available
- Prevent double-tap zoom on recording button
- Handle device orientation changes

### Performance
- <500ms recording start time
- Smooth 30fps animations
- Efficient memory management
- Battery usage optimization

## ♿ Accessibility

### WCAG 2.1 AA Compliance
- Screen reader support with ARIA labels
- Keyboard navigation (Space to record, Esc to close)
- High contrast colors
- Reduced motion support
- Focus management

### Keyboard Shortcuts
- **Space**: Start/stop recording
- **Escape**: Close panels
- **Tab**: Navigate interactive elements

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start
```

### Performance Checklist
- [ ] Bundle size <200KB
- [ ] First Contentful Paint <1.5s
- [ ] Cumulative Layout Shift <0.1
- [ ] Lighthouse Performance >90
- [ ] No console errors

### Security Headers
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Referrer-Policy: origin-when-cross-origin

## 🐛 Troubleshooting

### Common Issues

#### Microphone Not Working
1. Check browser permissions
2. Ensure HTTPS connection
3. Try different browser
4. Check system microphone settings

#### Speech Recognition Errors
1. Verify browser support
2. Check internet connection
3. Speak clearly and slowly
4. Try different language setting

#### Performance Issues
1. Close other browser tabs
2. Check available memory
3. Disable browser extensions
4. Update browser to latest version

### Debug Mode
```javascript
// Enable debug logging
localStorage.setItem('coach-me-debug', 'true');
```

## 🤝 Contributing

### Code Style
- TypeScript strict mode
- ESLint with strict rules
- Prettier for formatting
- Conventional commits

### Pull Request Process
1. Fork the repository
2. Create feature branch
3. Write tests for new features
4. Ensure all tests pass
5. Update documentation
6. Submit pull request

### Development Guidelines
- Follow domain-driven design principles
- Write comprehensive tests
- Document public APIs
- Optimize for performance
- Ensure accessibility compliance

## 📊 Performance Metrics

### Success Metrics
- Voice recording success rate: >95%
- Average transcription accuracy: >80%
- Time to start recording: <500ms
- Browser compatibility: >95% of target browsers
- Bundle size: <200KB compressed

### User Experience Metrics
- Task completion rate: >90% for basic recording
- Error recovery rate: >80% for permission issues
- Accessibility compliance: WCAG 2.1 AA
- Mobile performance: Smooth on low-end devices

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with Next.js, TypeScript, and Tailwind CSS
- Speech recognition powered by Web Speech API
- Audio processing using Web Audio API
- Testing with Jest and React Testing Library
