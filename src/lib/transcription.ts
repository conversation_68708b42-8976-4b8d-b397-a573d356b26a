/**
 * Transcription Pipeline
 * Handles both browser SpeechRecognition (fast path) and Whisper fallback
 */

import {
  TranscriptionResult,
  TranscriptionError,
  AudioRecording,
  SupportedLanguage,
  AudioErrorCode
} from './types';
import { isFeatureSupported, BrowserFeature } from './browserCompat';

/**
 * Configuration for transcription service
 */
export interface TranscriptionConfig {
  /** Preferred method: 'browser' | 'whisper' | 'auto' */
  preferredMethod: 'browser' | 'whisper' | 'auto';
  
  /** Timeout for transcription in milliseconds */
  timeoutMs: number;
  
  /** Minimum confidence threshold (0-1) */
  minConfidence: number;
  
  /** Whether to enable fallback to Whisper if browser fails */
  enableFallback: boolean;
  
  /** API endpoint for Whisper service */
  whisperEndpoint?: string;
}

/**
 * Default transcription configuration
 */
export const DEFAULT_TRANSCRIPTION_CONFIG: TranscriptionConfig = {
  preferredMethod: 'auto',
  timeoutMs: 10000, // 10 seconds
  minConfidence: 0.6,
  enableFallback: true,
  whisperEndpoint: '/api/transcribe',
};

/**
 * Browser-based transcription using SpeechRecognition API
 */
export class BrowserTranscriptionService {
  private recognition: SpeechRecognition | null = null;
  private config: TranscriptionConfig;

  constructor(config: TranscriptionConfig = DEFAULT_TRANSCRIPTION_CONFIG) {
    this.config = config;
  }

  /**
   * Check if browser transcription is available
   */
  isAvailable(): boolean {
    return isFeatureSupported(BrowserFeature.SPEECH_RECOGNITION);
  }

  /**
   * Transcribe audio using browser SpeechRecognition
   */
  async transcribe(
    audioBlob: Blob, 
    language: SupportedLanguage = SupportedLanguage.ENGLISH_US
  ): Promise<TranscriptionResult> {
    if (!this.isAvailable()) {
      throw this.createError(
        'Browser speech recognition not available',
        'unrecognized',
        false,
        true
      );
    }

    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      try {
        // Create recognition instance
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        // Configure recognition
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.maxAlternatives = 1;
        this.recognition.lang = language;

        // Set timeout
        const timeout = setTimeout(() => {
          if (this.recognition) {
            this.recognition.abort();
            reject(this.createError(
              'Transcription timeout',
              'timeout',
              true,
              true
            ));
          }
        }, this.config.timeoutMs);

        // Handle results
        this.recognition.onresult = (event) => {
          clearTimeout(timeout);
          
          const result = event.results[0];
          if (result && result[0]) {
            const transcript = result[0].transcript.trim();
            const confidence = result[0].confidence || 0.8; // Fallback confidence

            if (confidence >= this.config.minConfidence) {
              resolve({
                transcript,
                confidence,
                language,
                method: 'browser',
                processingTime: Date.now() - startTime,
                timestamp: new Date(),
              });
            } else {
              reject(this.createError(
                `Low confidence transcription: ${confidence}`,
                'unrecognized',
                true,
                true
              ));
            }
          } else {
            reject(this.createError(
              'No transcription result',
              'unrecognized',
              true,
              true
            ));
          }
        };

        // Handle errors
        this.recognition.onerror = (event) => {
          clearTimeout(timeout);
          
          let errorType: TranscriptionError['type'] = 'audio';
          let retryable = true;
          
          switch (event.error) {
            case 'not-allowed':
            case 'service-not-allowed':
              errorType = 'permission';
              retryable = false;
              break;
            case 'network':
              errorType = 'network';
              break;
            case 'no-speech':
            case 'audio-capture':
              errorType = 'audio';
              break;
            default:
              errorType = 'unrecognized';
          }

          reject(this.createError(
            `Speech recognition error: ${event.error}`,
            errorType,
            retryable,
            true
          ));
        };

        // Start recognition
        // Note: For real implementation, we'd need to play the audio blob
        // or use a different approach since SpeechRecognition works with live audio
        this.recognition.start();

      } catch (error) {
        reject(this.createError(
          `Failed to initialize speech recognition: ${error}`,
          'unrecognized',
          false,
          true
        ));
      }
    });
  }

  /**
   * Stop ongoing transcription
   */
  stop(): void {
    if (this.recognition) {
      this.recognition.abort();
      this.recognition = null;
    }
  }

  private createError(
    message: string,
    type: TranscriptionError['type'],
    retryable: boolean,
    fallbackAvailable: boolean
  ): TranscriptionError {
    return {
      code: AudioErrorCode.TRANSCRIPTION_FAILED,
      message,
      type,
      retryable,
      fallbackAvailable,
      recoverable: retryable,
      timestamp: new Date(),
    };
  }
}

/**
 * Whisper-based transcription service
 */
export class WhisperTranscriptionService {
  private config: TranscriptionConfig;

  constructor(config: TranscriptionConfig = DEFAULT_TRANSCRIPTION_CONFIG) {
    this.config = config;
  }

  /**
   * Check if Whisper service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.whisperEndpoint}/health`, {
        method: 'GET',
        timeout: 5000,
      } as RequestInit);
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Transcribe audio using Whisper API
   */
  async transcribe(
    audioBlob: Blob,
    language: SupportedLanguage = SupportedLanguage.ENGLISH_US
  ): Promise<TranscriptionResult> {
    const startTime = Date.now();

    try {
      // Prepare form data
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');
      formData.append('language', language);

      // Make API request
      const response = await fetch(this.config.whisperEndpoint!, {
        method: 'POST',
        body: formData,
        signal: AbortSignal.timeout(this.config.timeoutMs),
      });

      if (!response.ok) {
        throw new Error(`Whisper API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      // Validate response
      if (!result.transcript) {
        throw new Error('No transcript in Whisper response');
      }

      const confidence = result.confidence || 0.9; // Whisper typically has high confidence

      if (confidence < this.config.minConfidence) {
        throw new Error(`Low confidence transcription: ${confidence}`);
      }

      return {
        transcript: result.transcript.trim(),
        confidence,
        language: result.language || language,
        method: 'whisper',
        processingTime: Date.now() - startTime,
        timestamp: new Date(),
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      let errorType: TranscriptionError['type'] = 'network';
      if (errorMessage.includes('timeout')) {
        errorType = 'timeout';
      } else if (errorMessage.includes('confidence')) {
        errorType = 'unrecognized';
      }

      throw this.createError(
        `Whisper transcription failed: ${errorMessage}`,
        errorType,
        true,
        false
      );
    }
  }

  private createError(
    message: string,
    type: TranscriptionError['type'],
    retryable: boolean,
    fallbackAvailable: boolean
  ): TranscriptionError {
    return {
      code: AudioErrorCode.TRANSCRIPTION_FAILED,
      message,
      type,
      retryable,
      fallbackAvailable,
      recoverable: retryable,
      timestamp: new Date(),
    };
  }
}

/**
 * Main transcription service that orchestrates browser and Whisper services
 */
export class TranscriptionService {
  private browserService: BrowserTranscriptionService;
  private whisperService: WhisperTranscriptionService;
  private config: TranscriptionConfig;

  constructor(config: Partial<TranscriptionConfig> = {}) {
    this.config = { ...DEFAULT_TRANSCRIPTION_CONFIG, ...config };
    this.browserService = new BrowserTranscriptionService(this.config);
    this.whisperService = new WhisperTranscriptionService(this.config);
  }

  /**
   * Transcribe audio using the best available method
   */
  async transcribe(audioRecording: AudioRecording): Promise<TranscriptionResult> {
    const { audioBlob, language } = audioRecording;

    if (!audioBlob) {
      throw new Error('No audio blob provided for transcription');
    }

    // Determine transcription method
    const method = await this.selectTranscriptionMethod();

    try {
      switch (method) {
        case 'browser':
          return await this.browserService.transcribe(audioBlob, language);
        
        case 'whisper':
          return await this.whisperService.transcribe(audioBlob, language);
        
        default:
          throw new Error(`Unknown transcription method: ${method}`);
      }
    } catch (error) {
      // Try fallback if enabled and error supports it
      if (this.config.enableFallback && 
          error instanceof Object && 
          'fallbackAvailable' in error && 
          error.fallbackAvailable) {
        
        const fallbackMethod = method === 'browser' ? 'whisper' : 'browser';
        console.warn(`Transcription failed with ${method}, trying ${fallbackMethod}:`, error);
        
        try {
          if (fallbackMethod === 'whisper') {
            return await this.whisperService.transcribe(audioBlob, language);
          } else {
            return await this.browserService.transcribe(audioBlob, language);
          }
        } catch (fallbackError) {
          console.error('Fallback transcription also failed:', fallbackError);
          throw error; // Throw original error
        }
      }
      
      throw error;
    }
  }

  /**
   * Select the best transcription method based on configuration and availability
   */
  private async selectTranscriptionMethod(): Promise<'browser' | 'whisper'> {
    switch (this.config.preferredMethod) {
      case 'browser':
        return 'browser';
      
      case 'whisper':
        return 'whisper';
      
      case 'auto':
      default:
        // Prefer browser for speed, fallback to Whisper for accuracy
        if (this.browserService.isAvailable()) {
          return 'browser';
        } else if (await this.whisperService.isAvailable()) {
          return 'whisper';
        } else {
          throw new Error('No transcription service available');
        }
    }
  }

  /**
   * Stop any ongoing transcription
   */
  stop(): void {
    this.browserService.stop();
  }
}

// Export default instance
export const transcriptionService = new TranscriptionService();
