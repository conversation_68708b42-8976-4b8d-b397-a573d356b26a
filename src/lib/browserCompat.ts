/**
 * Browser compatibility and feature detection utilities
 * Provides comprehensive browser capability assessment and graceful degradation
 */

import {
  BrowserCapabilities,
  BrowserFeature,
  MissingFeature,
  PermissionState,
  Result,
  AudioError,
  AudioErrorCode,
} from './types';
import { MIN_BROWSER_VERSIONS, BROWSER_FEATURE_SUPPORT } from './constants';

// ============================================================================
// BROWSER DETECTION
// ============================================================================

/**
 * Detect browser type and version
 */
export function detectBrowser(): { name: string; version: string; platform: string } {
  const userAgent = navigator.userAgent;
  const platform = navigator.platform;

  // Chrome
  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    const match = userAgent.match(/Chrome\/(\d+)/);
    return {
      name: 'chrome',
      version: match ? match[1] : 'unknown',
      platform,
    };
  }

  // Edge
  if (userAgent.includes('Edg')) {
    const match = userAgent.match(/Edg\/(\d+)/);
    return {
      name: 'edge',
      version: match ? match[1] : 'unknown',
      platform,
    };
  }

  // Firefox
  if (userAgent.includes('Firefox')) {
    const match = userAgent.match(/Firefox\/(\d+)/);
    return {
      name: 'firefox',
      version: match ? match[1] : 'unknown',
      platform,
    };
  }

  // Safari
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    const match = userAgent.match(/Version\/(\d+)/);
    return {
      name: 'safari',
      version: match ? match[1] : 'unknown',
      platform,
    };
  }

  return {
    name: 'unknown',
    version: 'unknown',
    platform,
  };
}

// ============================================================================
// FEATURE DETECTION
// ============================================================================

/**
 * Check if a specific browser feature is supported
 */
export function isFeatureSupported(feature: BrowserFeature): boolean {
  switch (feature) {
    case BrowserFeature.MEDIA_DEVICES:
      return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

    case BrowserFeature.SPEECH_RECOGNITION:
      return !!(
        (window as any).SpeechRecognition ||
        (window as any).webkitSpeechRecognition
      );

    case BrowserFeature.MEDIA_RECORDER:
      return typeof MediaRecorder !== 'undefined';

    case BrowserFeature.AUDIO_CONTEXT:
      return !!(
        (window as any).AudioContext ||
        (window as any).webkitAudioContext
      );

    case BrowserFeature.BLOB_SUPPORT:
      return typeof Blob !== 'undefined' && typeof URL.createObjectURL === 'function';

    case BrowserFeature.MICROPHONE_ACCESS:
      return isFeatureSupported(BrowserFeature.MEDIA_DEVICES);

    default:
      return false;
  }
}

/**
 * Get all supported features
 */
export function getSupportedFeatures(): BrowserFeature[] {
  return Object.values(BrowserFeature).filter(isFeatureSupported);
}

/**
 * Get missing features with fallback suggestions
 */
export function getMissingFeatures(): MissingFeature[] {
  const missingFeatures: MissingFeature[] = [];

  Object.values(BrowserFeature).forEach((feature) => {
    if (!isFeatureSupported(feature)) {
      missingFeatures.push({
        feature,
        fallbackSuggestion: getFallbackSuggestion(feature),
        impact: getFeatureImpact(feature),
      });
    }
  });

  return missingFeatures;
}

/**
 * Get fallback suggestion for a missing feature
 */
function getFallbackSuggestion(feature: BrowserFeature): string {
  switch (feature) {
    case BrowserFeature.SPEECH_RECOGNITION:
      return 'Text input will be available as an alternative to voice recording';
    case BrowserFeature.MEDIA_RECORDER:
      return 'Audio recording will not be available, but text input will work';
    case BrowserFeature.MEDIA_DEVICES:
      return 'Please use a modern browser like Chrome, Firefox, or Safari';
    case BrowserFeature.AUDIO_CONTEXT:
      return 'Audio visualization will not be available';
    case BrowserFeature.BLOB_SUPPORT:
      return 'File download may not work properly';
    case BrowserFeature.MICROPHONE_ACCESS:
      return 'Voice recording requires microphone access';
    default:
      return 'Please update your browser for full functionality';
  }
}

/**
 * Get the impact level of a missing feature
 */
function getFeatureImpact(feature: BrowserFeature): 'critical' | 'major' | 'minor' {
  switch (feature) {
    case BrowserFeature.MEDIA_DEVICES:
    case BrowserFeature.MICROPHONE_ACCESS:
      return 'critical';
    case BrowserFeature.SPEECH_RECOGNITION:
    case BrowserFeature.MEDIA_RECORDER:
      return 'major';
    case BrowserFeature.AUDIO_CONTEXT:
    case BrowserFeature.BLOB_SUPPORT:
      return 'minor';
    default:
      return 'minor';
  }
}

// ============================================================================
// PERMISSION HANDLING
// ============================================================================

/**
 * Check current microphone permission state
 */
export async function checkMicrophonePermission(): Promise<PermissionState> {
  try {
    if (!navigator.permissions) {
      return PermissionState.UNKNOWN;
    }

    const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
    
    switch (permission.state) {
      case 'granted':
        return PermissionState.GRANTED;
      case 'denied':
        return PermissionState.DENIED;
      case 'prompt':
        return PermissionState.PROMPT;
      default:
        return PermissionState.UNKNOWN;
    }
  } catch (error) {
    console.warn('Permission API not supported:', error);
    return PermissionState.UNKNOWN;
  }
}

/**
 * Request microphone permission
 */
export async function requestMicrophonePermission(): Promise<Result<MediaStream>> {
  try {
    if (!isFeatureSupported(BrowserFeature.MEDIA_DEVICES)) {
      return {
        success: false,
        error: {
          code: AudioErrorCode.UNSUPPORTED_FEATURE,
          message: 'Microphone access is not supported in this browser',
          recoverable: false,
          timestamp: new Date(),
        },
      };
    }

    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    });

    return { success: true, data: stream };
  } catch (error) {
    const audioError = createPermissionError(error as Error);
    return { success: false, error: audioError };
  }
}

/**
 * Create appropriate error for permission failures
 */
function createPermissionError(error: Error): AudioError {
  const errorName = error.name;
  
  if (errorName === 'NotAllowedError') {
    return {
      code: AudioErrorCode.PERMISSION_DENIED,
      message: 'Microphone access was denied. Please enable microphone permissions.',
      details: error.message,
      recoverable: true,
      timestamp: new Date(),
      originalError: error,
    };
  }
  
  if (errorName === 'NotFoundError') {
    return {
      code: AudioErrorCode.DEVICE_NOT_FOUND,
      message: 'No microphone found. Please connect a microphone.',
      details: error.message,
      recoverable: true,
      timestamp: new Date(),
      originalError: error,
    };
  }
  
  if (errorName === 'NotReadableError') {
    return {
      code: AudioErrorCode.DEVICE_IN_USE,
      message: 'Microphone is being used by another application.',
      details: error.message,
      recoverable: true,
      timestamp: new Date(),
      originalError: error,
    };
  }

  return {
    code: AudioErrorCode.UNKNOWN_ERROR,
    message: 'Failed to access microphone.',
    details: error.message,
    recoverable: true,
    timestamp: new Date(),
    originalError: error,
  };
}

// ============================================================================
// CAPABILITY ASSESSMENT
// ============================================================================

/**
 * Assess overall browser capabilities
 */
export async function assessBrowserCapabilities(): Promise<BrowserCapabilities> {
  const browser = detectBrowser();
  const supportedFeatures = getSupportedFeatures();
  const missingFeatures = getMissingFeatures();
  const warnings: string[] = [];

  // Calculate compatibility score
  const totalFeatures = Object.values(BrowserFeature).length;
  const compatibilityScore = Math.round((supportedFeatures.length / totalFeatures) * 100);

  // Add browser-specific warnings
  if (browser.name === 'safari' && browser.platform.includes('iPhone')) {
    warnings.push('iOS Safari has limited background processing capabilities');
  }

  if (browser.name === 'firefox') {
    warnings.push('Firefox has limited speech recognition support');
  }

  // Check minimum version requirements
  const minVersion = MIN_BROWSER_VERSIONS[browser.name as keyof typeof MIN_BROWSER_VERSIONS];
  if (minVersion && parseInt(browser.version) < minVersion) {
    warnings.push(`Browser version ${browser.version} is below recommended minimum ${minVersion}`);
  }

  return {
    compatibilityScore,
    supportedFeatures,
    missingFeatures,
    warnings,
    browser,
  };
}

/**
 * Check if the browser supports the minimum required features
 */
export function hasMinimumSupport(): boolean {
  const requiredFeatures = [
    BrowserFeature.MEDIA_DEVICES,
    BrowserFeature.BLOB_SUPPORT,
  ];

  return requiredFeatures.every(isFeatureSupported);
}

/**
 * Get recommended browser upgrade message
 */
export function getBrowserUpgradeMessage(): string | null {
  const browser = detectBrowser();
  
  if (!hasMinimumSupport()) {
    return `Your browser (${browser.name} ${browser.version}) doesn't support voice recording. Please upgrade to Chrome 66+, Firefox 55+, Safari 14+, or Edge 79+ for the best experience.`;
  }

  const missingFeatures = getMissingFeatures();
  const criticalMissing = missingFeatures.filter(f => f.impact === 'critical');
  
  if (criticalMissing.length > 0) {
    return `Some features may not work properly in ${browser.name} ${browser.version}. Consider upgrading your browser for full functionality.`;
  }

  return null;
}
