/**
 * Core domain types for the Coach Me voice recording application
 * These types serve as the single source of truth for all audio operations
 */

// ============================================================================
// CORE DOMAIN TYPES
// ============================================================================

/**
 * Supported languages for speech recognition and UI
 */
export enum SupportedLanguage {
  ENGLISH_US = 'en-US',
  HEBREW_IL = 'he-IL',
}

/**
 * Language configuration with RTL support
 */
export interface LanguageConfig {
  code: SupportedLanguage;
  name: string;
  isRTL: boolean;
  speechRecognitionSupported: boolean;
}

/**
 * Recording metadata for technical context and debugging
 */
export interface RecordingMetadata {
  deviceType: 'mobile' | 'desktop' | 'tablet';
  browserType: string;
  browserVersion: string;
  audioQuality: {
    sampleRate: number;
    channels: number;
    bitrate?: number;
  };
  recordingEnvironment: {
    hasNoiseCancellation: boolean;
    hasEchoCancellation: boolean;
    hasAutoGainControl: boolean;
  };
  timestamp: Date;
}

/**
 * Core audio recording domain object
 */
export interface AudioRecording {
  /** Unique identifier using UUID */
  id: string;
  
  /** Raw transcript from speech recognition */
  transcript: string;
  
  /** Speech recognition confidence score (0-1) */
  confidence: number;
  
  /** Audio blob data (nullable for text-only mode) */
  audioBlob: Blob | null;
  
  /** Recording duration in milliseconds */
  duration: number;
  
  /** Language used for recording */
  language: SupportedLanguage;
  
  /** Creation timestamp */
  timestamp: Date;
  
  /** Technical metadata for debugging and optimization */
  metadata: RecordingMetadata;
}

// ============================================================================
// STATE MANAGEMENT TYPES
// ============================================================================

/**
 * Recording status using discriminated unions for type safety
 */
export enum RecordingStatus {
  IDLE = 'idle',
  REQUESTING_PERMISSION = 'requesting_permission',
  INITIALIZING = 'initializing',
  RECORDING = 'recording',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
}

/**
 * Current state of the recording process
 */
export interface RecordingState {
  status: RecordingStatus;
  transcript: string;
  interimTranscript: string;
  confidence: number;
  duration: number;
  audioLevel: number;
  error: AudioError | null;
  isSupported: boolean;
}

// ============================================================================
// ERROR HANDLING TYPES
// ============================================================================

/**
 * Error code categories for programmatic handling
 */
export enum AudioErrorCode {
  // Permission errors
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  DEVICE_IN_USE = 'DEVICE_IN_USE',
  
  // Technical errors
  RECORDING_FAILED = 'RECORDING_FAILED',
  TRANSCRIPTION_FAILED = 'TRANSCRIPTION_FAILED',
  AUDIO_PROCESSING_FAILED = 'AUDIO_PROCESSING_FAILED',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  
  // Compatibility errors
  UNSUPPORTED_BROWSER = 'UNSUPPORTED_BROWSER',
  UNSUPPORTED_FEATURE = 'UNSUPPORTED_FEATURE',
  
  // Generic errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * Structured error object for comprehensive error handling
 */
export interface AudioError {
  /** Error code for programmatic handling */
  code: AudioErrorCode;
  
  /** User-friendly message for display */
  message: string;
  
  /** Technical details for debugging (don't show to users) */
  details?: string;
  
  /** Whether the user can retry the operation */
  recoverable: boolean;
  
  /** Timestamp when error occurred */
  timestamp: Date;
  
  /** Original error object if available */
  originalError?: Error;
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

/**
 * Audio processing configuration
 */
export interface AudioConfig {
  readonly sampleRate: number;
  readonly channels: number;
  readonly echoCancellation: boolean;
  readonly noiseSuppression: boolean;
  readonly autoGainControl: boolean;
  readonly maxDuration: number; // milliseconds
  readonly maxFileSize: number; // bytes
}

/**
 * Speech recognition configuration
 */
export interface SpeechRecognitionConfig {
  readonly continuous: boolean;
  readonly interimResults: boolean;
  readonly maxAlternatives: number;
  readonly language: SupportedLanguage;
}

/**
 * Recording limits and constraints
 */
export interface RecordingLimits {
  readonly maxDurationMs: number;
  readonly maxFileSizeBytes: number;
  readonly minDurationMs: number;
  readonly maxSilenceDurationMs: number;
}

// ============================================================================
// BROWSER COMPATIBILITY TYPES
// ============================================================================

/**
 * Browser capability assessment
 */
export interface BrowserCapabilities {
  /** Overall compatibility score (0-100) */
  compatibilityScore: number;
  
  /** Supported features */
  supportedFeatures: BrowserFeature[];
  
  /** Missing features with fallback suggestions */
  missingFeatures: MissingFeature[];
  
  /** Performance warnings */
  warnings: string[];
  
  /** Browser identification */
  browser: {
    name: string;
    version: string;
    platform: string;
  };
}

/**
 * Individual browser feature support
 */
export enum BrowserFeature {
  MEDIA_DEVICES = 'MEDIA_DEVICES',
  SPEECH_RECOGNITION = 'SPEECH_RECOGNITION',
  MEDIA_RECORDER = 'MEDIA_RECORDER',
  AUDIO_CONTEXT = 'AUDIO_CONTEXT',
  BLOB_SUPPORT = 'BLOB_SUPPORT',
  MICROPHONE_ACCESS = 'MICROPHONE_ACCESS',
}

/**
 * Missing feature with fallback recommendation
 */
export interface MissingFeature {
  feature: BrowserFeature;
  fallbackSuggestion: string;
  impact: 'critical' | 'major' | 'minor';
}

/**
 * Permission states for microphone access
 */
export enum PermissionState {
  GRANTED = 'granted',
  DENIED = 'denied',
  PROMPT = 'prompt',
  UNKNOWN = 'unknown',
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Generic result type for operations that can fail
 */
export type Result<T, E = AudioError> = 
  | { success: true; data: T }
  | { success: false; error: E };

/**
 * Callback function types
 */
export type AudioRecordingCallback = (recording: AudioRecording) => void;
export type ErrorCallback = (error: AudioError) => void;
export type ProgressCallback = (progress: { duration: number; audioLevel: number }) => void;

/**
 * Hook return type for audio recording
 */
export interface UseAudioRecordingReturn {
  // State
  state: RecordingState;

  // Actions
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<AudioRecording | null>;
  clearError: () => void;
  reset: () => void;

  // Data
  audioBlob: Blob | null;
  finalTranscript: string;
  capabilities: BrowserCapabilities | null;
}

// ============================================================================
// PHASE 2: AI PROMPT REFINEMENT TYPES
// ============================================================================

/**
 * Result of transcription process (browser or Whisper)
 */
export interface TranscriptionResult {
  transcript: string;
  confidence: number; // 0-1
  language: SupportedLanguage;
  method: 'browser' | 'whisper';
  processingTime: number; // milliseconds
  timestamp: Date;
}

/**
 * Error types for transcription failures
 */
export interface TranscriptionError extends AudioError {
  type: 'permission' | 'audio' | 'network' | 'unrecognized' | 'timeout';
  retryable: boolean;
  fallbackAvailable: boolean;
}

/**
 * User's motivation response after initial recording
 */
export interface MotivationResponse {
  text: string;
  method: 'voice' | 'text';
  timestamp: Date;
  confidence?: number; // Only for voice responses
}

/**
 * AI-refined coaching prompt result
 */
export interface RefinedPromptResult {
  agentPrompt: string;
  time: string; // e.g. "every day at 08:00"
  followUpQuestion?: string;
  confidence: number; // AI confidence in the refinement
  processingTime: number; // milliseconds
}

/**
 * Finalized prompt ready for scheduling
 */
export interface FinalizedPrompt {
  id: string;
  originalTranscript: string;
  motivation: string;
  refined: RefinedPromptResult;
  schedule: {
    type: 'daily' | 'weekly' | 'monthly' | 'custom';
    time: string; // UTC time
    timezone: string;
  };
  confirmedAt: Date;
  userId?: string; // For future multi-user support
}

/**
 * Scheduled prompt for delivery
 */
export interface ScheduledPrompt {
  id: string;
  promptId: string; // Reference to FinalizedPrompt
  scheduledFor: Date; // UTC
  status: 'pending' | 'delivered' | 'failed' | 'cancelled';
  deliveryMethod: 'notification' | 'email' | 'webhook';
  retryCount: number;
  lastAttempt?: Date;
  deliveredAt?: Date;
  error?: string;
}
