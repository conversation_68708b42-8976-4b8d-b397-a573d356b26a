/**
 * Application constants and configuration
 * All configuration values are immutable and type-safe
 */

import { 
  AudioConfig, 
  SpeechRecognitionConfig, 
  RecordingLimits, 
  LanguageConfig, 
  SupportedLanguage 
} from './types';

// ============================================================================
// AUDIO CONFIGURATION
// ============================================================================

/**
 * Default audio processing configuration
 */
export const DEFAULT_AUDIO_CONFIG: AudioConfig = {
  sampleRate: 44100,
  channels: 1, // Mono for voice recording
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
  maxDuration: 300000, // 5 minutes in milliseconds
  maxFileSize: 10 * 1024 * 1024, // 10MB
} as const;

/**
 * Fallback audio configuration for older browsers
 */
export const FALLBACK_AUDIO_CONFIG: AudioConfig = {
  sampleRate: 22050,
  channels: 1,
  echoCancellation: false,
  noiseSuppression: false,
  autoGainControl: false,
  maxDuration: 180000, // 3 minutes
  maxFileSize: 5 * 1024 * 1024, // 5MB
} as const;

// ============================================================================
// SPEECH RECOGNITION CONFIGURATION
// ============================================================================

/**
 * Default speech recognition configuration
 */
export const DEFAULT_SPEECH_CONFIG: SpeechRecognitionConfig = {
  continuous: true,
  interimResults: true,
  maxAlternatives: 1,
  language: SupportedLanguage.ENGLISH_US,
} as const;

// ============================================================================
// RECORDING LIMITS
// ============================================================================

/**
 * Recording constraints and limits
 */
export const RECORDING_LIMITS: RecordingLimits = {
  maxDurationMs: 300000, // 5 minutes
  maxFileSizeBytes: 10 * 1024 * 1024, // 10MB
  minDurationMs: 1000, // 1 second minimum
  maxSilenceDurationMs: 10000, // 10 seconds of silence before auto-stop
} as const;

// ============================================================================
// LANGUAGE CONFIGURATION
// ============================================================================

/**
 * Supported languages with their configurations
 */
export const LANGUAGE_CONFIGS: Record<SupportedLanguage, LanguageConfig> = {
  [SupportedLanguage.ENGLISH_US]: {
    code: SupportedLanguage.ENGLISH_US,
    name: 'English (US)',
    isRTL: false,
    speechRecognitionSupported: true,
  },
  [SupportedLanguage.HEBREW_IL]: {
    code: SupportedLanguage.HEBREW_IL,
    name: 'עברית (ישראל)',
    isRTL: true,
    speechRecognitionSupported: true, // Chrome supports Hebrew
  },
} as const;

// ============================================================================
// BROWSER COMPATIBILITY
// ============================================================================

/**
 * Minimum browser versions for full feature support
 */
export const MIN_BROWSER_VERSIONS = {
  chrome: 66,
  firefox: 55,
  safari: 14,
  edge: 79,
} as const;

/**
 * Browser-specific feature support matrix
 */
export const BROWSER_FEATURE_SUPPORT = {
  chrome: {
    speechRecognition: true,
    mediaRecorder: true,
    audioContext: true,
    echoCancellation: true,
  },
  firefox: {
    speechRecognition: false, // Limited support
    mediaRecorder: true,
    audioContext: true,
    echoCancellation: true,
  },
  safari: {
    speechRecognition: true,
    mediaRecorder: true,
    audioContext: true,
    echoCancellation: false, // Limited support
  },
  edge: {
    speechRecognition: true,
    mediaRecorder: true,
    audioContext: true,
    echoCancellation: true,
  },
} as const;

// ============================================================================
// UI CONFIGURATION
// ============================================================================

/**
 * Animation durations for UI feedback
 */
export const ANIMATION_DURATIONS = {
  recordingPulse: 1000, // milliseconds
  buttonTransition: 200,
  errorFadeIn: 300,
  successFadeIn: 400,
} as const;

/**
 * Audio level visualization configuration
 */
export const AUDIO_VISUALIZATION = {
  updateInterval: 100, // milliseconds
  smoothingTimeConstant: 0.8,
  minDecibels: -90,
  maxDecibels: -10,
  fftSize: 256,
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

/**
 * User-friendly error messages
 */
export const ERROR_MESSAGES = {
  PERMISSION_DENIED: 'Microphone access is required. Please enable microphone permissions and try again.',
  DEVICE_NOT_FOUND: 'No microphone found. Please connect a microphone and try again.',
  DEVICE_IN_USE: 'Microphone is being used by another application. Please close other apps and try again.',
  RECORDING_FAILED: 'Recording failed. Please try again.',
  TRANSCRIPTION_FAILED: 'Speech recognition failed. You can still save your recording.',
  NETWORK_ERROR: 'Network connection lost. Your recording will be saved locally.',
  UNSUPPORTED_BROWSER: 'Your browser doesn\'t support voice recording. Please use Chrome, Firefox, or Safari.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

/**
 * Recovery suggestions for different error types
 */
export const RECOVERY_SUGGESTIONS = {
  PERMISSION_DENIED: [
    'Click the microphone icon in your browser\'s address bar',
    'Select "Allow" for microphone access',
    'Refresh the page and try again',
  ],
  DEVICE_NOT_FOUND: [
    'Check that your microphone is connected',
    'Try a different microphone',
    'Check your system audio settings',
  ],
  UNSUPPORTED_BROWSER: [
    'Use Google Chrome for the best experience',
    'Update your browser to the latest version',
    'Try Firefox or Safari as alternatives',
  ],
} as const;

// ============================================================================
// PERFORMANCE CONFIGURATION
// ============================================================================

/**
 * Performance optimization settings
 */
export const PERFORMANCE_CONFIG = {
  audioLevelUpdateThrottle: 50, // milliseconds
  transcriptUpdateDebounce: 300, // milliseconds
  maxConcurrentRecordings: 1,
  memoryCleanupInterval: 30000, // 30 seconds
} as const;

/**
 * Feature flags for experimental features
 */
export const FEATURE_FLAGS = {
  enableRealTimeTranscription: true,
  enableAudioVisualization: true,
  enableOfflineMode: false,
  enableMultiLanguageDetection: false,
} as const;
