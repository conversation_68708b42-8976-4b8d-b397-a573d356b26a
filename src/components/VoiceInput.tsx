/**
 * Voice Input Component
 * Beautiful, accessible voice recording component with real-time feedback
 */

'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { Mic, MicOff, Square, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { clsx } from 'clsx';
import { useAudioRecording } from '@/hooks/useAudioRecording';
import {
  AudioRecording,
  AudioError,
  RecordingStatus,
  SupportedLanguage,
} from '@/lib/types';
import { ANIMATION_DURATIONS } from '@/lib/constants';

// ============================================================================
// COMPONENT PROPS
// ============================================================================

export interface VoiceInputProps {
  /** Callback when recording is completed */
  onRecordingComplete?: (recording: AudioRecording) => void;
  
  /** Error callback */
  onError?: (error: AudioError) => void;
  
  /** Language for speech recognition */
  language?: SupportedLanguage;
  
  /** Maximum recording duration in milliseconds */
  maxDuration?: number;
  
  /** Whether the component is disabled */
  disabled?: boolean;
  
  /** Additional CSS classes */
  className?: string;
  
  /** Show transcript in real-time */
  showTranscript?: boolean;
  
  /** Show audio level visualization */
  showAudioLevel?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function VoiceInput({
  onRecordingComplete,
  onError,
  language = SupportedLanguage.ENGLISH_US,
  maxDuration = 300000, // 5 minutes
  disabled = false,
  className,
  showTranscript = true,
  showAudioLevel = true,
}: VoiceInputProps) {
  const {
    state,
    startRecording,
    stopRecording,
    clearError,
    reset,
    capabilities,
  } = useAudioRecording(language);

  const [isAnimating, setIsAnimating] = useState(false);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Handle recording completion
  useEffect(() => {
    if (state.status === RecordingStatus.COMPLETED && onRecordingComplete) {
      stopRecording().then((recording) => {
        if (recording) {
          onRecordingComplete(recording);
        }
      });
    }
  }, [state.status, onRecordingComplete, stopRecording]);

  // Handle errors
  useEffect(() => {
    if (state.error && onError) {
      onError(state.error);
    }
  }, [state.error, onError]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleRecordingToggle = useCallback(async () => {
    if (disabled) return;

    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), ANIMATION_DURATIONS.buttonTransition);

    try {
      if (state.status === RecordingStatus.RECORDING) {
        await stopRecording();
      } else if (state.status === RecordingStatus.IDLE || state.status === RecordingStatus.ERROR) {
        if (state.error) {
          clearError();
        }
        await startRecording();
      }
    } catch (error) {
      console.error('Recording toggle failed:', error);
    }
  }, [disabled, state.status, state.error, startRecording, stopRecording, clearError]);

  const handleReset = useCallback(() => {
    reset();
  }, [reset]);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const getButtonIcon = () => {
    switch (state.status) {
      case RecordingStatus.RECORDING:
        return <Square className="w-6 h-6" />;
      case RecordingStatus.REQUESTING_PERMISSION:
      case RecordingStatus.INITIALIZING:
      case RecordingStatus.PROCESSING:
        return <Loader2 className="w-6 h-6 animate-spin" />;
      case RecordingStatus.COMPLETED:
        return <CheckCircle className="w-6 h-6" />;
      case RecordingStatus.ERROR:
        return <AlertCircle className="w-6 h-6" />;
      default:
        return capabilities?.compatibilityScore && capabilities.compatibilityScore < 50 
          ? <MicOff className="w-6 h-6" />
          : <Mic className="w-6 h-6" />;
    }
  };

  const getButtonColor = () => {
    switch (state.status) {
      case RecordingStatus.RECORDING:
        return 'bg-red-500 hover:bg-red-600 text-white';
      case RecordingStatus.COMPLETED:
        return 'bg-green-500 hover:bg-green-600 text-white';
      case RecordingStatus.ERROR:
        return 'bg-red-500 hover:bg-red-600 text-white';
      case RecordingStatus.REQUESTING_PERMISSION:
      case RecordingStatus.INITIALIZING:
      case RecordingStatus.PROCESSING:
        return 'bg-blue-500 text-white cursor-not-allowed';
      default:
        return disabled || (capabilities?.compatibilityScore && capabilities.compatibilityScore < 50)
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
          : 'bg-blue-500 hover:bg-blue-600 text-white';
    }
  };

  const getStatusText = () => {
    switch (state.status) {
      case RecordingStatus.REQUESTING_PERMISSION:
        return 'Requesting microphone access...';
      case RecordingStatus.INITIALIZING:
        return 'Initializing recording...';
      case RecordingStatus.RECORDING:
        return 'Recording... Click to stop';
      case RecordingStatus.PROCESSING:
        return 'Processing recording...';
      case RecordingStatus.COMPLETED:
        return 'Recording completed!';
      case RecordingStatus.ERROR:
        return state.error?.message || 'An error occurred';
      default:
        return capabilities?.compatibilityScore && capabilities.compatibilityScore < 50
          ? 'Voice recording not supported'
          : 'Click to start recording';
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isButtonDisabled = (): boolean => {
    return disabled ||
           state.status === RecordingStatus.REQUESTING_PERMISSION ||
           state.status === RecordingStatus.INITIALIZING ||
           state.status === RecordingStatus.PROCESSING ||
           (capabilities?.compatibilityScore ? capabilities.compatibilityScore < 50 : false);
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={clsx('flex flex-col items-center space-y-4', className)}>
      {/* Main Recording Button */}
      <div className="relative">
        <button
          onClick={handleRecordingToggle}
          disabled={isButtonDisabled()}
          className={clsx(
            'relative w-20 h-20 rounded-full transition-all duration-200 transform',
            'focus:outline-none focus:ring-4 focus:ring-blue-300',
            'shadow-lg hover:shadow-xl',
            getButtonColor(),
            isAnimating && 'scale-95',
            state.status === RecordingStatus.RECORDING && 'animate-pulse'
          )}
          aria-label={getStatusText()}
        >
          {getButtonIcon()}
          
          {/* Audio Level Visualization */}
          {showAudioLevel && state.status === RecordingStatus.RECORDING && (
            <div 
              className="absolute inset-0 rounded-full border-4 border-white opacity-60"
              style={{
                transform: `scale(${1 + state.audioLevel * 0.3})`,
                transition: 'transform 0.1s ease-out',
              }}
            />
          )}
        </button>

        {/* Recording Duration */}
        {state.status === RecordingStatus.RECORDING && (
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
            <span className="text-sm font-mono text-gray-600 bg-white px-2 py-1 rounded shadow">
              {formatDuration(state.duration)}
            </span>
          </div>
        )}
      </div>

      {/* Status Text */}
      <div className="text-center">
        <p className={clsx(
          'text-sm font-medium transition-colors duration-200',
          state.status === RecordingStatus.ERROR ? 'text-red-600' : 'text-gray-700'
        )}>
          {getStatusText()}
        </p>
      </div>

      {/* Real-time Transcript */}
      {showTranscript && (state.transcript || state.interimTranscript) && (
        <div className="w-full max-w-md">
          <div className="bg-gray-50 rounded-lg p-4 border">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Transcript:</h4>
            <div className="text-sm text-gray-900 min-h-[2rem]">
              <span>{state.transcript}</span>
              {state.interimTranscript && (
                <span className="text-gray-500 italic"> {state.interimTranscript}</span>
              )}
            </div>
            {state.confidence > 0 && (
              <div className="mt-2 text-xs text-gray-500">
                Confidence: {Math.round(state.confidence * 100)}%
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {state.error && (
        <div className="w-full max-w-md">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800 mb-1">
                  Recording Error
                </h4>
                <p className="text-sm text-red-700 mb-3">
                  {state.error.message}
                </p>
                {state.error.recoverable && (
                  <div className="flex space-x-2">
                    <button
                      onClick={clearError}
                      className="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors"
                    >
                      Try Again
                    </button>
                    <button
                      onClick={handleReset}
                      className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded transition-colors"
                    >
                      Reset
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Browser Compatibility Warning */}
      {capabilities && capabilities.compatibilityScore < 80 && capabilities.warnings.length > 0 && (
        <div className="w-full max-w-md">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start">
              <AlertCircle className="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
              <div className="text-xs text-yellow-800">
                <p className="font-medium mb-1">Browser Compatibility Notice:</p>
                <ul className="list-disc list-inside space-y-1">
                  {capabilities.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
