/**
 * Motivation Capture Component
 * Captures user's motivation after initial voice recording
 */

'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>cO<PERSON>, Type, SkipForward, Edit3, Check, X } from 'lucide-react';
import { MotivationResponse, SupportedLanguage, TranscriptionResult } from '@/lib/types';
import { transcriptionService } from '@/lib/transcription';

export interface MotivationCaptureProps {
  /** Callback when motivation is captured */
  onMotivationCaptured: (motivation: MotivationResponse) => void;
  
  /** Callback when user skips motivation */
  onSkipped: () => void;
  
  /** Language for voice input */
  language: SupportedLanguage;
  
  /** Whether to show the component */
  isVisible: boolean;
  
  /** Additional CSS classes */
  className?: string;
}

export function MotivationCapture({
  onMotivationCaptured,
  onSkipped,
  language,
  isVisible,
  className = '',
}: MotivationCaptureProps) {
  const [inputMethod, setInputMethod] = useState<'voice' | 'text'>('voice');
  const [isRecording, setIsRecording] = useState(false);
  const [textInput, setTextInput] = useState('');
  const [voiceTranscript, setVoiceTranscript] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-focus text input when switching to text mode
  useEffect(() => {
    if (inputMethod === 'text' && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [inputMethod]);

  // Clear error when input changes
  useEffect(() => {
    if (error) {
      setError(null);
    }
  }, [textInput, voiceTranscript, error]);

  /**
   * Start voice recording
   */
  const startVoiceRecording = useCallback(async () => {
    try {
      setError(null);
      setIsRecording(true);
      audioChunksRef.current = [];

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        await processVoiceRecording(audioBlob);
        
        // Clean up
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
    } catch (error) {
      setError('Failed to start recording. Please check microphone permissions.');
      setIsRecording(false);
    }
  }, []);

  /**
   * Stop voice recording
   */
  const stopVoiceRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  }, [isRecording]);

  /**
   * Process voice recording and transcribe
   */
  const processVoiceRecording = useCallback(async (audioBlob: Blob) => {
    setIsProcessing(true);
    
    try {
      // Create a mock AudioRecording for transcription
      const audioRecording = {
        id: `motivation-${Date.now()}`,
        transcript: '',
        confidence: 0,
        audioBlob,
        duration: 0,
        language,
        timestamp: new Date(),
        metadata: {
          deviceType: 'desktop' as const,
          browserType: 'chrome',
          browserVersion: '91',
          audioQuality: { sampleRate: 44100, channels: 1 },
          recordingEnvironment: {
            hasNoiseCancellation: true,
            hasEchoCancellation: true,
            hasAutoGainControl: true,
          },
          timestamp: new Date(),
        },
      };

      const result: TranscriptionResult = await transcriptionService.transcribe(audioRecording);
      setVoiceTranscript(result.transcript);
      setIsEditing(true); // Allow user to review/edit
    } catch (error) {
      setError('Failed to transcribe audio. Please try again or use text input.');
    } finally {
      setIsProcessing(false);
    }
  }, [language]);

  /**
   * Submit motivation response
   */
  const submitMotivation = useCallback(() => {
    const text = inputMethod === 'voice' ? voiceTranscript : textInput;
    
    if (!text.trim()) {
      setError('Please provide your motivation or skip this step.');
      return;
    }

    const motivation: MotivationResponse = {
      text: text.trim(),
      method: inputMethod,
      timestamp: new Date(),
    };

    onMotivationCaptured(motivation);
  }, [inputMethod, voiceTranscript, textInput, onMotivationCaptured]);

  /**
   * Handle skip
   */
  const handleSkip = useCallback(() => {
    onSkipped();
  }, [onSkipped]);

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      {/* Header */}
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Why is this important to you?
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Understanding your motivation helps create more personalized coaching prompts.
        </p>
      </div>

      {/* Input Method Toggle */}
      <div className="flex justify-center mb-6">
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-1 flex">
          <button
            onClick={() => setInputMethod('voice')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              inputMethod === 'voice'
                ? 'bg-white dark:bg-gray-600 text-voice shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <Mic className="w-4 h-4" />
            <span>Voice</span>
          </button>
          <button
            onClick={() => setInputMethod('text')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              inputMethod === 'text'
                ? 'bg-white dark:bg-gray-600 text-voice shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <Type className="w-4 h-4" />
            <span>Text</span>
          </button>
        </div>
      </div>

      {/* Voice Input */}
      {inputMethod === 'voice' && (
        <div className="space-y-4">
          {/* Voice Recording Button */}
          <div className="flex justify-center">
            <button
              onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
              disabled={isProcessing}
              className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-200 ${
                isRecording
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                  : 'bg-voice hover:bg-voice-dark'
              } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isRecording ? (
                <MicOff className="w-6 h-6 text-white" />
              ) : (
                <Mic className="w-6 h-6 text-white" />
              )}
            </button>
          </div>

          {/* Recording Status */}
          <div className="text-center">
            {isRecording && (
              <p className="text-red-500 font-medium">Recording... Click to stop</p>
            )}
            {isProcessing && (
              <p className="text-voice font-medium">Processing audio...</p>
            )}
          </div>

          {/* Voice Transcript */}
          {voiceTranscript && (
            <div className="space-y-3">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Transcription:
                  </span>
                  <button
                    onClick={() => setIsEditing(!isEditing)}
                    className="text-voice hover:text-voice-dark"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                </div>
                
                {isEditing ? (
                  <textarea
                    value={voiceTranscript}
                    onChange={(e) => setVoiceTranscript(e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                    rows={3}
                    placeholder="Edit your transcription..."
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{voiceTranscript}</p>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Text Input */}
      {inputMethod === 'text' && (
        <div className="space-y-4">
          <textarea
            ref={textareaRef}
            value={textInput}
            onChange={(e) => setTextInput(e.target.value)}
            placeholder="Type your motivation here..."
            className="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-voice focus:border-transparent"
            rows={4}
          />
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between mt-6">
        <button
          onClick={handleSkip}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <SkipForward className="w-4 h-4" />
          <span>Skip</span>
        </button>

        <div className="flex space-x-3">
          {(voiceTranscript || textInput) && (
            <button
              onClick={() => {
                setVoiceTranscript('');
                setTextInput('');
                setIsEditing(false);
                setError(null);
              }}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Clear</span>
            </button>
          )}
          
          <button
            onClick={submitMotivation}
            disabled={!voiceTranscript && !textInput.trim()}
            className="flex items-center space-x-2 px-6 py-2 bg-voice hover:bg-voice-dark disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            <Check className="w-4 h-4" />
            <span>Continue</span>
          </button>
        </div>
      </div>
    </div>
  );
}
