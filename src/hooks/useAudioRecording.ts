/**
 * Custom hook for audio recording with speech recognition
 * Handles the complete recording lifecycle with comprehensive error handling
 */

import { useReducer, useRef, useCallback, useEffect } from 'react';
import {
  RecordingState,
  RecordingStatus,
  AudioRecording,
  AudioError,
  AudioErrorCode,
  UseAudioRecordingReturn,
  SupportedLanguage,
  BrowserCapabilities,
  RecordingMetadata,
} from '@/lib/types';
import {
  DEFAULT_AUDIO_CONFIG,
  DEFAULT_SPEECH_CONFIG,
  RECORDING_LIMITS,
  PERFORMANCE_CONFIG,
} from '@/lib/constants';
import {
  assessBrowserCapabilities,
  requestMicrophonePermission,
  detectBrowser,
} from '@/lib/browserCompat';

// ============================================================================
// STATE MANAGEMENT
// ============================================================================

interface RecordingAction {
  type: 'SET_STATUS' | 'SET_TRANSCRIPT' | 'SET_ERROR' | 'SET_AUDIO_LEVEL' | 'SET_DURATION' | 'RESET';
  payload?: any;
}

const initialState: RecordingState = {
  status: RecordingStatus.IDLE,
  transcript: '',
  interimTranscript: '',
  confidence: 0,
  duration: 0,
  audioLevel: 0,
  error: null,
  isSupported: false,
};

function recordingReducer(state: RecordingState, action: RecordingAction): RecordingState {
  switch (action.type) {
    case 'SET_STATUS':
      return { ...state, status: action.payload, error: null };
    
    case 'SET_TRANSCRIPT':
      return {
        ...state,
        transcript: action.payload.final || state.transcript,
        interimTranscript: action.payload.interim || '',
        confidence: action.payload.confidence || state.confidence,
      };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, status: RecordingStatus.ERROR };
    
    case 'SET_AUDIO_LEVEL':
      return { ...state, audioLevel: action.payload };
    
    case 'SET_DURATION':
      return { ...state, duration: action.payload };
    
    case 'RESET':
      return { ...initialState, isSupported: state.isSupported };
    
    default:
      return state;
  }
}

// ============================================================================
// MAIN HOOK
// ============================================================================

export function useAudioRecording(
  language: SupportedLanguage = SupportedLanguage.ENGLISH_US
): UseAudioRecordingReturn {
  const [state, dispatch] = useReducer(recordingReducer, initialState);
  
  // Refs for managing recording resources
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const speechRecognitionRef = useRef<SpeechRecognition | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const startTimeRef = useRef<number>(0);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const audioLevelIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const capabilitiesRef = useRef<BrowserCapabilities | null>(null);

  // ============================================================================
  // INITIALIZATION
  // ============================================================================

  useEffect(() => {
    let mounted = true;

    const initializeCapabilities = async () => {
      try {
        const capabilities = await assessBrowserCapabilities();
        if (mounted) {
          capabilitiesRef.current = capabilities;
          dispatch({
            type: 'SET_STATUS',
            payload: capabilities.compatibilityScore > 50 ? RecordingStatus.IDLE : RecordingStatus.ERROR,
          });
          
          if (capabilities.compatibilityScore <= 50) {
            dispatch({
              type: 'SET_ERROR',
              payload: createError(
                AudioErrorCode.UNSUPPORTED_BROWSER,
                'Your browser has limited support for voice recording'
              ),
            });
          }
        }
      } catch (error) {
        if (mounted) {
          dispatch({
            type: 'SET_ERROR',
            payload: createError(AudioErrorCode.UNKNOWN_ERROR, 'Failed to assess browser capabilities'),
          });
        }
      }
    };

    initializeCapabilities();

    return () => {
      mounted = false;
    };
  }, []);

  // ============================================================================
  // CLEANUP
  // ============================================================================

  const cleanup = useCallback(() => {
    // Stop all intervals
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    
    if (audioLevelIntervalRef.current) {
      clearInterval(audioLevelIntervalRef.current);
      audioLevelIntervalRef.current = null;
    }

    // Stop speech recognition
    if (speechRecognitionRef.current) {
      speechRecognitionRef.current.stop();
      speechRecognitionRef.current = null;
    }

    // Stop media recorder
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;
    }

    // Close audio context
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // Stop media stream
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => track.stop());
      mediaStreamRef.current = null;
    }

    // Clear audio chunks
    audioChunksRef.current = [];
  }, []);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // ============================================================================
  // AUDIO LEVEL MONITORING
  // ============================================================================

  const setupAudioLevelMonitoring = useCallback((stream: MediaStream) => {
    try {
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      
      analyser.smoothingTimeConstant = 0.8;
      analyser.fftSize = 256;
      
      microphone.connect(analyser);
      
      audioContextRef.current = audioContext;
      analyserRef.current = analyser;

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      
      const updateAudioLevel = () => {
        if (analyserRef.current && state.status === RecordingStatus.RECORDING) {
          analyserRef.current.getByteFrequencyData(dataArray);
          
          // Calculate RMS (Root Mean Square) for audio level
          let sum = 0;
          for (let i = 0; i < dataArray.length; i++) {
            sum += dataArray[i] * dataArray[i];
          }
          const rms = Math.sqrt(sum / dataArray.length);
          const audioLevel = Math.min(rms / 128, 1); // Normalize to 0-1
          
          dispatch({ type: 'SET_AUDIO_LEVEL', payload: audioLevel });
        }
      };

      audioLevelIntervalRef.current = setInterval(
        updateAudioLevel,
        PERFORMANCE_CONFIG.audioLevelUpdateThrottle
      );
    } catch (error) {
      console.warn('Failed to setup audio level monitoring:', error);
    }
  }, [state.status]);

  // ============================================================================
  // SPEECH RECOGNITION SETUP
  // ============================================================================

  const setupSpeechRecognition = useCallback(() => {
    try {
      const SpeechRecognition = 
        window.SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      if (!SpeechRecognition) {
        console.warn('Speech recognition not supported');
        return;
      }

      const recognition = new SpeechRecognition();
      recognition.continuous = DEFAULT_SPEECH_CONFIG.continuous;
      recognition.interimResults = DEFAULT_SPEECH_CONFIG.interimResults;
      recognition.maxAlternatives = DEFAULT_SPEECH_CONFIG.maxAlternatives;
      recognition.lang = language;

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let finalTranscript = '';
        let interimTranscript = '';
        let confidence = 0;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const transcript = result[0].transcript;
          
          if (result.isFinal) {
            finalTranscript += transcript;
            confidence = result[0].confidence;
          } else {
            interimTranscript += transcript;
          }
        }

        dispatch({
          type: 'SET_TRANSCRIPT',
          payload: {
            final: finalTranscript,
            interim: interimTranscript,
            confidence,
          },
        });
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.warn('Speech recognition error:', event.error);
        if (event.error === 'no-speech') {
          // Don't treat no-speech as a critical error
          return;
        }
        
        dispatch({
          type: 'SET_ERROR',
          payload: createError(
            AudioErrorCode.TRANSCRIPTION_FAILED,
            'Speech recognition failed'
          ),
        });
      };

      recognition.onend = () => {
        // Restart recognition if still recording
        if (state.status === RecordingStatus.RECORDING && speechRecognitionRef.current) {
          try {
            recognition.start();
          } catch (error) {
            console.warn('Failed to restart speech recognition:', error);
          }
        }
      };

      speechRecognitionRef.current = recognition;
      recognition.start();
    } catch (error) {
      console.warn('Failed to setup speech recognition:', error);
    }
  }, [language, state.status]);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const createError = (code: AudioErrorCode, message: string, originalError?: Error): AudioError => ({
    code,
    message,
    details: originalError?.message,
    recoverable: true,
    timestamp: new Date(),
    originalError,
  });

  const generateRecordingId = (): string => {
    return `recording_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const createRecordingMetadata = (): RecordingMetadata => {
    const browser = detectBrowser();
    return {
      deviceType: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
      browserType: browser.name,
      browserVersion: browser.version,
      audioQuality: {
        sampleRate: DEFAULT_AUDIO_CONFIG.sampleRate,
        channels: DEFAULT_AUDIO_CONFIG.channels,
      },
      recordingEnvironment: {
        hasNoiseCancellation: DEFAULT_AUDIO_CONFIG.noiseSuppression,
        hasEchoCancellation: DEFAULT_AUDIO_CONFIG.echoCancellation,
        hasAutoGainControl: DEFAULT_AUDIO_CONFIG.autoGainControl,
      },
      timestamp: new Date(),
    };
  };

  // ============================================================================
  // PUBLIC API
  // ============================================================================

  const startRecording = useCallback(async () => {
    try {
      dispatch({ type: 'SET_STATUS', payload: RecordingStatus.REQUESTING_PERMISSION });

      // Request microphone permission
      const permissionResult = await requestMicrophonePermission();
      if (!permissionResult.success) {
        dispatch({ type: 'SET_ERROR', payload: permissionResult.error });
        return;
      }

      const stream = permissionResult.data;
      mediaStreamRef.current = stream;

      dispatch({ type: 'SET_STATUS', payload: RecordingStatus.INITIALIZING });

      // Setup media recorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        dispatch({ type: 'SET_STATUS', payload: RecordingStatus.PROCESSING });
      };

      mediaRecorderRef.current = mediaRecorder;

      // Setup audio level monitoring
      setupAudioLevelMonitoring(stream);

      // Setup speech recognition
      setupSpeechRecognition();

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      startTimeRef.current = Date.now();

      // Start duration tracking
      durationIntervalRef.current = setInterval(() => {
        const duration = Date.now() - startTimeRef.current;
        dispatch({ type: 'SET_DURATION', payload: duration });

        // Auto-stop if max duration reached
        if (duration >= RECORDING_LIMITS.maxDurationMs) {
          stopRecording();
        }
      }, 100);

      dispatch({ type: 'SET_STATUS', payload: RecordingStatus.RECORDING });
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: createError(
          AudioErrorCode.RECORDING_FAILED,
          'Failed to start recording',
          error as Error
        ),
      });
    }
  }, [setupAudioLevelMonitoring, setupSpeechRecognition]);

  const stopRecording = useCallback(async (): Promise<AudioRecording | null> => {
    try {
      if (state.status !== RecordingStatus.RECORDING) {
        return null;
      }

      dispatch({ type: 'SET_STATUS', payload: RecordingStatus.PROCESSING });

      // Stop media recorder
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }

      // Stop speech recognition
      if (speechRecognitionRef.current) {
        speechRecognitionRef.current.stop();
      }

      // Create audio blob
      const audioBlob = audioChunksRef.current.length > 0
        ? new Blob(audioChunksRef.current, { type: 'audio/webm' })
        : null;

      // Create recording object
      const recording: AudioRecording = {
        id: generateRecordingId(),
        transcript: state.transcript,
        confidence: state.confidence,
        audioBlob,
        duration: state.duration,
        language,
        timestamp: new Date(),
        metadata: createRecordingMetadata(),
      };

      // Cleanup resources
      cleanup();

      dispatch({ type: 'SET_STATUS', payload: RecordingStatus.COMPLETED });

      return recording;
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: createError(
          AudioErrorCode.RECORDING_FAILED,
          'Failed to stop recording',
          error as Error
        ),
      });
      return null;
    }
  }, [state.status, state.transcript, state.confidence, state.duration, language, cleanup]);

  const clearError = useCallback(() => {
    dispatch({ type: 'SET_STATUS', payload: RecordingStatus.IDLE });
  }, []);

  const reset = useCallback(() => {
    cleanup();
    dispatch({ type: 'RESET' });
  }, [cleanup]);

  return {
    state,
    startRecording,
    stopRecording,
    clearError,
    reset,
    audioBlob: audioChunksRef.current.length > 0 
      ? new Blob(audioChunksRef.current, { type: 'audio/webm' }) 
      : null,
    finalTranscript: state.transcript,
    capabilities: capabilitiesRef.current,
  };
}
