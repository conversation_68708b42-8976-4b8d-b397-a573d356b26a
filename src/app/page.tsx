/**
 * Coach Me - Main Application Page
 * Voice-first personal development application
 */

'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Mic, History, HelpCircle, ChevronUp } from 'lucide-react';

import { VoiceInput } from '@/components/VoiceInput';
import { AudioRecording, AudioError, SupportedLanguage } from '@/lib/types';

export default function Home() {
  const [recordings, setRecordings] = useState<AudioRecording[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    SupportedLanguage.ENGLISH_US
  );

  // Handle recording completion
  const handleRecordingComplete = useCallback((recording: AudioRecording) => {
    setRecordings(prev => [recording, ...prev.slice(0, 9)]); // Keep last 10 recordings
  }, []);

  // Handle recording errors
  const handleRecordingError = useCallback((error: AudioError) => {
    // Error is already handled by VoiceInput component
    // Just log for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Recording error:', error);
    }
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Spacebar to trigger recording (when not in input field)
      if (event.code === 'Space' && event.target === document.body) {
        event.preventDefault();
        // The VoiceInput component will handle the recording logic
      }

      // Escape to close help/history
      if (event.code === 'Escape') {
        setShowHelp(false);
        setShowHistory(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <header className="w-full px-4 py-6 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-voice to-voice-dark rounded-lg flex items-center justify-center">
              <Mic className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Coach Me
            </h1>
          </div>

          <div className="flex items-center space-x-2">
            {/* Language Toggle */}
            <select
              value={currentLanguage}
              onChange={(e) => setCurrentLanguage(e.target.value as SupportedLanguage)}
              className="text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-voice"
              aria-label="Select language"
            >
              <option value={SupportedLanguage.ENGLISH_US}>English</option>
              <option value={SupportedLanguage.HEBREW_IL}>עברית</option>
            </select>

            {/* History Toggle */}
            <button
              onClick={() => setShowHistory(!showHistory)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label="Toggle recording history"
            >
              <History className="w-5 h-5" />
            </button>

            {/* Help Toggle */}
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label="Toggle help"
            >
              <HelpCircle className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 px-4 sm:px-6 lg:px-8 pb-8">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
              Speak Your Thoughts
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Transform your voice into structured insights. Tap the microphone and share what&apos;s on your mind.
            </p>
          </div>

          {/* Voice Input Section */}
          <div className="flex flex-col items-center space-y-8">
            <VoiceInput
              onRecordingComplete={handleRecordingComplete}
              onError={handleRecordingError}
              language={currentLanguage}
              showTranscript={true}
              showAudioLevel={true}
              className="w-full max-w-md"
            />

            {/* Keyboard Shortcut Hint */}
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
              <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs">Space</kbd>
              {' '}to record • {' '}
              <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs">Esc</kbd>
              {' '}to close panels
            </p>
          </div>

          {/* Recording History */}
          {showHistory && (
            <div className="mt-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Recent Recordings
                  </h3>
                  <button
                    onClick={() => setShowHistory(false)}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    aria-label="Close history"
                  >
                    <ChevronUp className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                {recordings.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                    No recordings yet. Start by recording your first thought!
                  </p>
                ) : (
                  <div className="space-y-4">
                    {recordings.map((recording) => (
                      <div
                        key={recording.id}
                        className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(recording.timestamp).toLocaleString()}
                          </span>
                          <span className="text-xs bg-voice/10 text-voice px-2 py-1 rounded">
                            {Math.round(recording.confidence * 100)}% confidence
                          </span>
                        </div>
                        <p className="text-gray-900 dark:text-white">
                          {recording.transcript || 'No transcript available'}
                        </p>
                        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                          Duration: {Math.round(recording.duration / 1000)}s •
                          Language: {recording.language}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Help Section */}
          {showHelp && (
            <div className="mt-12 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    How to Use Coach Me
                  </h3>
                  <button
                    onClick={() => setShowHelp(false)}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    aria-label="Close help"
                  >
                    <ChevronUp className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-6">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Getting Started
                  </h4>
                  <ol className="list-decimal list-inside space-y-1 text-gray-600 dark:text-gray-300">
                    <li>Click the microphone button or press the spacebar</li>
                    <li>Allow microphone access when prompted</li>
                    <li>Speak your thoughts clearly</li>
                    <li>Click the stop button or press spacebar again to finish</li>
                  </ol>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Tips for Better Results
                  </h4>
                  <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-300">
                    <li>Speak clearly and at a normal pace</li>
                    <li>Use a quiet environment when possible</li>
                    <li>Keep recordings under 5 minutes for best performance</li>
                    <li>Try different languages using the language selector</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Keyboard Shortcuts
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Start/Stop Recording:</span>
                      <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Space</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Close Panels:</span>
                      <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Esc</kbd>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="mt-16 border-t border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50">
        <div className="max-w-4xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            <p>
              Coach Me - Transform your thoughts into insights through voice
            </p>
            <p className="mt-1">
              Built with Next.js, TypeScript, and Tailwind CSS
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
