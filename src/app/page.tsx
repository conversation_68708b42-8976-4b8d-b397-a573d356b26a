'use client';

import { useState } from 'react';
import { VoiceInput } from '@/components/VoiceInput';
import { AudioRecording, AudioError, SupportedLanguage } from '@/lib/types';

export default function Home() {
  const [recordings, setRecordings] = useState<AudioRecording[]>([]);
  const [lastError, setLastError] = useState<AudioError | null>(null);

  const handleRecordingComplete = (recording: AudioRecording) => {
    setRecordings(prev => [recording, ...prev]);
    setLastError(null);
  };

  const handleError = (error: AudioError) => {
    setLastError(error);
  };

  const formatTimestamp = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Coach Me - Voice Recording
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transform your spontaneous thoughts into structured coaching prompts.
            Click the microphone to start recording your voice.
          </p>
        </div>

        {/* Voice Input Component */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <VoiceInput
            onRecordingComplete={handleRecordingComplete}
            onError={handleError}
            language={SupportedLanguage.ENGLISH_US}
            showTranscript={true}
            showAudioLevel={true}
            className="w-full"
          />
        </div>

        {/* Language Selector */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Language Settings</h3>
          <div className="flex space-x-4">
            <VoiceInput
              onRecordingComplete={handleRecordingComplete}
              onError={handleError}
              language={SupportedLanguage.ENGLISH_US}
              showTranscript={false}
              showAudioLevel={false}
              className="flex-1"
            />
            <div className="text-sm text-gray-600 self-center">English (US)</div>
          </div>
          <div className="flex space-x-4 mt-4">
            <VoiceInput
              onRecordingComplete={handleRecordingComplete}
              onError={handleError}
              language={SupportedLanguage.HEBREW_IL}
              showTranscript={false}
              showAudioLevel={false}
              className="flex-1"
            />
            <div className="text-sm text-gray-600 self-center">עברית (ישראל)</div>
          </div>
        </div>

        {/* Recent Recordings */}
        {recordings.length > 0 && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Recordings ({recordings.length})
            </h3>
            <div className="space-y-4">
              {recordings.slice(0, 5).map((recording) => (
                <div
                  key={recording.id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="text-sm text-gray-500">
                      {formatTimestamp(recording.timestamp)} • {recording.language}
                    </div>
                    <div className="text-sm text-gray-500">
                      {Math.round(recording.duration / 1000)}s
                    </div>
                  </div>

                  {recording.transcript && (
                    <div className="mb-3">
                      <p className="text-gray-900">{recording.transcript}</p>
                      {recording.confidence > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          Confidence: {Math.round(recording.confidence * 100)}%
                        </div>
                      )}
                    </div>
                  )}

                  {recording.audioBlob && (
                    <audio
                      controls
                      className="w-full h-8"
                      src={URL.createObjectURL(recording.audioBlob)}
                    />
                  )}

                  <div className="text-xs text-gray-400 mt-2">
                    Device: {recording.metadata.deviceType} •
                    Browser: {recording.metadata.browserType} {recording.metadata.browserVersion}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Error Display */}
        {lastError && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-6 mt-8">
            <h3 className="text-lg font-semibold text-red-800 mb-2">Last Error</h3>
            <p className="text-red-700 mb-2">{lastError.message}</p>
            <div className="text-sm text-red-600">
              Code: {lastError.code} • Time: {formatTimestamp(lastError.timestamp)}
            </div>
            {lastError.details && (
              <div className="text-xs text-red-500 mt-2 font-mono">
                {lastError.details}
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mt-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">How to Use</h3>
          <ul className="text-blue-800 space-y-2">
            <li>• Click the microphone button to start recording</li>
            <li>• Speak clearly into your microphone</li>
            <li>• Watch the real-time transcript appear below</li>
            <li>• Click the stop button (square) to finish recording</li>
            <li>• Your recording will appear in the list below</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
