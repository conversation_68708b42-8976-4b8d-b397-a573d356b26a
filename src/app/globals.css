@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Coach Me Brand Colors */
  --brand-primary: #3b82f6;
  --brand-secondary: #6366f1;
  --brand-accent: #10b981;
  --brand-warning: #f59e0b;
  --brand-error: #ef4444;

  /* Voice Recording States */
  --recording-active: #ef4444;
  --recording-idle: #3b82f6;
  --recording-processing: #6366f1;
  --recording-success: #10b981;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom brand colors */
  --color-brand-primary: var(--brand-primary);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-accent: var(--brand-accent);
  --color-brand-warning: var(--brand-warning);
  --color-brand-error: var(--brand-error);

  /* Recording state colors */
  --color-recording-active: var(--recording-active);
  --color-recording-idle: var(--recording-idle);
  --color-recording-processing: var(--recording-processing);
  --color-recording-success: var(--recording-success);

  /* Custom animations */
  --animate-recording-pulse: recording-pulse 1s ease-in-out infinite;
  --animate-audio-level: audio-level 0.1s ease-out;
  --animate-button-press: button-press 0.2s ease-out;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom Animations for Voice Recording */
@keyframes recording-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes audio-level {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(var(--scale, 1));
  }
}

@keyframes button-press {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for better accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-4 focus:ring-blue-300;
}

/* RTL support for Hebrew */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(-1);
}
