@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Brand Colors for <PERSON> Me */
  --primary: #3b82f6;      /* Blue - trust, communication */
  --primary-dark: #1d4ed8;
  --secondary: #10b981;    /* Green - growth, success */
  --secondary-dark: #047857;
  --accent: #f59e0b;       /* Amber - energy, motivation */
  --accent-dark: #d97706;
  --voice: #8b5cf6;        /* Purple - voice, creativity */
  --voice-dark: #7c3aed;
  --error: #ef4444;        /* Red - errors, warnings */
  --success: #22c55e;      /* Green - success states */
  --warning: #f59e0b;      /* Amber - warnings */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Brand Color System */
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-secondary-dark: var(--secondary-dark);
  --color-accent: var(--accent);
  --color-accent-dark: var(--accent-dark);
  --color-voice: var(--voice);
  --color-voice-dark: var(--voice-dark);
  --color-error: var(--error);
  --color-success: var(--success);
  --color-warning: var(--warning);

  /* Voice Recording Animations */
  --animate-pulse-voice: pulse-voice 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-recording: recording 1.5s ease-in-out infinite;
  --animate-audio-wave: audio-wave 0.8s ease-in-out infinite;
  --animate-voice-feedback: voice-feedback 0.3s ease-out;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Dark mode brand colors */
    --primary: #60a5fa;
    --primary-dark: #3b82f6;
    --secondary: #34d399;
    --secondary-dark: #10b981;
    --accent: #fbbf24;
    --accent-dark: #f59e0b;
    --voice: #a78bfa;
    --voice-dark: #8b5cf6;
  }
}

/* Custom Animations for Voice Recording */
@keyframes pulse-voice {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes recording {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
  }
}

@keyframes audio-wave {
  0%, 100% {
    height: 20%;
  }
  50% {
    height: 100%;
  }
}

@keyframes voice-feedback {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
