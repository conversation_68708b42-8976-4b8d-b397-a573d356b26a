/**
 * Whisper Transcription API Endpoint
 * Handles audio file transcription using OpenAI Whisper or local Whisper instance
 */

import { NextRequest, NextResponse } from 'next/server';
import { SupportedLanguage } from '@/lib/types';

/**
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({ 
    status: 'healthy',
    service: 'whisper-transcription',
    timestamp: new Date().toISOString()
  });
}

/**
 * Transcribe audio file
 */
export async function POST(request: NextRequest) {
  try {
    // Parse form data
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const language = formData.get('language') as SupportedLanguage || SupportedLanguage.ENGLISH_US;

    // Validate input
    if (!audioFile) {
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    // Validate file size (max 25MB for Whisper API)
    const maxSize = 25 * 1024 * 1024; // 25MB
    if (audioFile.size > maxSize) {
      return NextResponse.json(
        { error: 'Audio file too large. Maximum size is 25MB.' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = [
      'audio/webm',
      'audio/mp4',
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
    ];
    
    if (!allowedTypes.includes(audioFile.type)) {
      return NextResponse.json(
        { error: `Unsupported audio format: ${audioFile.type}` },
        { status: 400 }
      );
    }

    const startTime = Date.now();

    // Choose transcription method based on environment
    const useOpenAI = process.env.OPENAI_API_KEY && process.env.NODE_ENV === 'production';
    
    let result;
    if (useOpenAI) {
      result = await transcribeWithOpenAI(audioFile, language);
    } else {
      result = await transcribeWithMockService(audioFile, language);
    }

    const processingTime = Date.now() - startTime;

    return NextResponse.json({
      transcript: result.transcript,
      confidence: result.confidence,
      language: result.language,
      processingTime,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Transcription error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      { 
        error: 'Transcription failed',
        details: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Transcribe using OpenAI Whisper API
 */
async function transcribeWithOpenAI(audioFile: File, language: SupportedLanguage) {
  const formData = new FormData();
  formData.append('file', audioFile);
  formData.append('model', 'whisper-1');
  
  // Map our language codes to OpenAI format
  const languageMap: Record<SupportedLanguage, string> = {
    [SupportedLanguage.ENGLISH_US]: 'en',
    [SupportedLanguage.HEBREW_IL]: 'he',
  };
  
  formData.append('language', languageMap[language] || 'en');
  formData.append('response_format', 'verbose_json');

  const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
  }

  const data = await response.json();

  return {
    transcript: data.text || '',
    confidence: calculateConfidenceFromSegments(data.segments),
    language: data.language || language,
  };
}

/**
 * Mock transcription service for development
 */
async function transcribeWithMockService(audioFile: File, language: SupportedLanguage) {
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  // Mock transcription based on language
  const mockTranscripts = {
    [SupportedLanguage.ENGLISH_US]: [
      "I want to exercise more regularly and build a healthy routine.",
      "I need to improve my time management and be more productive.",
      "I want to learn a new skill and challenge myself intellectually.",
      "I should practice gratitude and maintain a positive mindset.",
      "I want to strengthen my relationships and communicate better.",
    ],
    [SupportedLanguage.HEBREW_IL]: [
      "אני רוצה להתאמן יותר באופן קבוע ולבנות שגרה בריאה.",
      "אני צריך לשפר את ניהול הזמן שלי ולהיות יותר פרודוקטיבי.",
      "אני רוצה ללמוד מיומנות חדשה ולאתגר את עצמי אינטלקטואלית.",
      "אני צריך להתרגל להכרת תודה ולשמור על גישה חיובית.",
      "אני רוצה לחזק את מערכות היחסים שלי ולתקשר טוב יותר.",
    ],
  };

  const transcripts = mockTranscripts[language] || mockTranscripts[SupportedLanguage.ENGLISH_US];
  const randomTranscript = transcripts[Math.floor(Math.random() * transcripts.length)];

  // Simulate varying confidence based on "audio quality"
  const confidence = 0.75 + Math.random() * 0.2; // 0.75 - 0.95

  return {
    transcript: randomTranscript,
    confidence,
    language,
  };
}

/**
 * Calculate confidence score from OpenAI Whisper segments
 */
function calculateConfidenceFromSegments(segments: any[]): number {
  if (!segments || segments.length === 0) {
    return 0.8; // Default confidence
  }

  // Calculate average confidence from segments
  const totalConfidence = segments.reduce((sum, segment) => {
    return sum + (segment.avg_logprob || -0.5); // Default to moderate confidence
  }, 0);

  const avgLogProb = totalConfidence / segments.length;
  
  // Convert log probability to confidence score (0-1)
  // Log probs are typically negative, closer to 0 is better
  const confidence = Math.max(0, Math.min(1, (avgLogProb + 1) / 1));
  
  return confidence;
}

/**
 * Rate limiting and caching could be added here for production use
 */
