/**
 * Tests for transcription service
 */

import { 
  TranscriptionService, 
  BrowserTranscriptionService, 
  WhisperTranscriptionService,
  DEFAULT_TRANSCRIPTION_CONFIG 
} from '@/lib/transcription';
import { AudioRecording, SupportedLanguage } from '@/lib/types';

// Mock fetch for Whisper API tests
global.fetch = jest.fn();

// Mock SpeechRecognition
const mockSpeechRecognition = {
  start: jest.fn(),
  stop: jest.fn(),
  abort: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  onresult: null as any,
  onerror: null as any,
  onend: null as any,
  continuous: false,
  interimResults: false,
  maxAlternatives: 1,
  lang: 'en-US',
};

// Mock browser APIs
Object.defineProperty(window, 'SpeechRecognition', {
  writable: true,
  value: jest.fn(() => mockSpeechRecognition),
});

Object.defineProperty(window, 'webkitSpeechRecognition', {
  writable: true,
  value: jest.fn(() => mockSpeechRecognition),
});

describe('TranscriptionService', () => {
  let transcriptionService: TranscriptionService;
  let mockAudioRecording: AudioRecording;

  beforeEach(() => {
    jest.clearAllMocks();
    transcriptionService = new TranscriptionService();
    
    // Create mock audio recording
    const mockBlob = new Blob(['mock audio data'], { type: 'audio/webm' });
    mockAudioRecording = {
      id: 'test-recording-1',
      transcript: '',
      confidence: 0,
      audioBlob: mockBlob,
      duration: 5000,
      language: SupportedLanguage.ENGLISH_US,
      timestamp: new Date(),
      metadata: {
        deviceType: 'desktop',
        browserType: 'chrome',
        browserVersion: '91',
        audioQuality: { sampleRate: 44100, channels: 1 },
        recordingEnvironment: {
          hasNoiseCancellation: true,
          hasEchoCancellation: true,
          hasAutoGainControl: true,
        },
        timestamp: new Date(),
      },
    };
  });

  describe('Configuration', () => {
    it('uses default configuration', () => {
      const service = new TranscriptionService();
      expect(service).toBeDefined();
    });

    it('accepts custom configuration', () => {
      const customConfig = {
        preferredMethod: 'whisper' as const,
        timeoutMs: 15000,
        minConfidence: 0.8,
      };
      
      const service = new TranscriptionService(customConfig);
      expect(service).toBeDefined();
    });
  });

  describe('Method Selection', () => {
    it('selects browser method when available and preferred', async () => {
      const service = new TranscriptionService({ preferredMethod: 'browser' });
      
      // Mock successful browser transcription
      mockSpeechRecognition.onresult = jest.fn();
      
      try {
        await service.transcribe(mockAudioRecording);
      } catch (error) {
        // Expected to fail in test environment, but method selection logic is tested
      }
    });

    it('selects whisper method when preferred', async () => {
      const service = new TranscriptionService({ preferredMethod: 'whisper' });
      
      // Mock successful Whisper response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          transcript: 'Test transcription',
          confidence: 0.9,
          language: 'en-US',
        }),
      });

      const result = await service.transcribe(mockAudioRecording);
      expect(result.transcript).toBe('Test transcription');
      expect(result.method).toBe('whisper');
    });
  });

  describe('Error Handling', () => {
    it('throws error when no audio blob provided', async () => {
      const recordingWithoutBlob = { ...mockAudioRecording, audioBlob: null };
      
      await expect(transcriptionService.transcribe(recordingWithoutBlob))
        .rejects.toThrow('No audio blob provided for transcription');
    });

    it('handles network errors gracefully', async () => {
      const service = new TranscriptionService({ 
        preferredMethod: 'whisper',
        enableFallback: false 
      });
      
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(service.transcribe(mockAudioRecording))
        .rejects.toThrow('Network error');
    });
  });
});

describe('BrowserTranscriptionService', () => {
  let browserService: BrowserTranscriptionService;

  beforeEach(() => {
    browserService = new BrowserTranscriptionService();
    jest.clearAllMocks();
  });

  describe('Availability Check', () => {
    it('returns true when SpeechRecognition is available', () => {
      expect(browserService.isAvailable()).toBe(true);
    });

    it('returns false when SpeechRecognition is not available', () => {
      // Temporarily remove SpeechRecognition
      const originalSR = window.SpeechRecognition;
      const originalWebkitSR = window.webkitSpeechRecognition;
      
      delete (window as any).SpeechRecognition;
      delete (window as any).webkitSpeechRecognition;
      
      expect(browserService.isAvailable()).toBe(false);
      
      // Restore
      window.SpeechRecognition = originalSR;
      window.webkitSpeechRecognition = originalWebkitSR;
    });
  });

  describe('Transcription', () => {
    it('configures recognition correctly', async () => {
      const mockBlob = new Blob(['test'], { type: 'audio/webm' });
      
      try {
        await browserService.transcribe(mockBlob, SupportedLanguage.ENGLISH_US);
      } catch (error) {
        // Expected to fail, but we can check if recognition was configured
      }

      expect(mockSpeechRecognition.continuous).toBe(false);
      expect(mockSpeechRecognition.interimResults).toBe(false);
      expect(mockSpeechRecognition.maxAlternatives).toBe(1);
      expect(mockSpeechRecognition.lang).toBe(SupportedLanguage.ENGLISH_US);
    });

    it('handles successful recognition', async () => {
      const mockBlob = new Blob(['test'], { type: 'audio/webm' });
      
      // Mock successful result
      setTimeout(() => {
        if (mockSpeechRecognition.onresult) {
          mockSpeechRecognition.onresult({
            results: [{
              0: {
                transcript: 'Hello world',
                confidence: 0.9,
              },
            }],
          } as any);
        }
      }, 100);

      const result = await browserService.transcribe(mockBlob);
      expect(result.transcript).toBe('Hello world');
      expect(result.confidence).toBe(0.9);
      expect(result.method).toBe('browser');
    });

    it('handles recognition errors', async () => {
      const mockBlob = new Blob(['test'], { type: 'audio/webm' });
      
      // Mock error
      setTimeout(() => {
        if (mockSpeechRecognition.onerror) {
          mockSpeechRecognition.onerror({
            error: 'not-allowed',
          } as any);
        }
      }, 100);

      await expect(browserService.transcribe(mockBlob))
        .rejects.toMatchObject({
          type: 'permission',
          retryable: false,
        });
    });
  });
});

describe('WhisperTranscriptionService', () => {
  let whisperService: WhisperTranscriptionService;

  beforeEach(() => {
    whisperService = new WhisperTranscriptionService();
    jest.clearAllMocks();
  });

  describe('Availability Check', () => {
    it('returns true when health check succeeds', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
      });

      const isAvailable = await whisperService.isAvailable();
      expect(isAvailable).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith('/api/transcribe/health', {
        method: 'GET',
        timeout: 5000,
      });
    });

    it('returns false when health check fails', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const isAvailable = await whisperService.isAvailable();
      expect(isAvailable).toBe(false);
    });
  });

  describe('Transcription', () => {
    it('sends correct request to API', async () => {
      const mockBlob = new Blob(['test audio'], { type: 'audio/webm' });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          transcript: 'Test transcription',
          confidence: 0.95,
          language: 'en-US',
        }),
      });

      const result = await whisperService.transcribe(mockBlob, SupportedLanguage.ENGLISH_US);
      
      expect(result.transcript).toBe('Test transcription');
      expect(result.confidence).toBe(0.95);
      expect(result.method).toBe('whisper');
      
      // Check that FormData was sent correctly
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0];
      expect(fetchCall[0]).toBe('/api/transcribe');
      expect(fetchCall[1].method).toBe('POST');
      expect(fetchCall[1].body).toBeInstanceOf(FormData);
    });

    it('handles API errors', async () => {
      const mockBlob = new Blob(['test audio'], { type: 'audio/webm' });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      await expect(whisperService.transcribe(mockBlob))
        .rejects.toMatchObject({
          type: 'network',
          retryable: true,
        });
    });

    it('handles low confidence results', async () => {
      const mockBlob = new Blob(['test audio'], { type: 'audio/webm' });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          transcript: 'Unclear speech',
          confidence: 0.3, // Below default threshold
        }),
      });

      await expect(whisperService.transcribe(mockBlob))
        .rejects.toMatchObject({
          type: 'unrecognized',
          retryable: true,
        });
    });
  });
});

describe('Integration Tests', () => {
  it('falls back from browser to whisper on error', async () => {
    const service = new TranscriptionService({
      preferredMethod: 'browser',
      enableFallback: true,
    });

    const mockBlob = new Blob(['test'], { type: 'audio/webm' });
    const mockRecording = {
      ...mockAudioRecording,
      audioBlob: mockBlob,
    };

    // Mock browser failure
    setTimeout(() => {
      if (mockSpeechRecognition.onerror) {
        mockSpeechRecognition.onerror({
          error: 'network',
        } as any);
      }
    }, 100);

    // Mock successful Whisper fallback
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        transcript: 'Fallback transcription',
        confidence: 0.9,
      }),
    });

    const result = await service.transcribe(mockRecording);
    expect(result.transcript).toBe('Fallback transcription');
    expect(result.method).toBe('whisper');
  });
});
