/**
 * Test file to verify type definitions compile correctly
 */

import {
  AudioRecording,
  RecordingState,
  AudioError,
  BrowserCapabilities,
  SupportedLanguage,
  RecordingStatus,
  AudioErrorCode,
  BrowserFeature,
} from '@/lib/types';

// Test that all types can be imported and used
describe('Type Definitions', () => {
  test('AudioRecording type should be valid', () => {
    const recording: AudioRecording = {
      id: 'test-id',
      transcript: 'Hello world',
      confidence: 0.95,
      audioBlob: null,
      duration: 5000,
      language: SupportedLanguage.ENGLISH_US,
      timestamp: new Date(),
      metadata: {
        deviceType: 'desktop',
        browserType: 'chrome',
        browserVersion: '91',
        audioQuality: {
          sampleRate: 44100,
          channels: 1,
        },
        recordingEnvironment: {
          hasNoiseCancellation: true,
          hasEchoCancellation: true,
          hasAutoGainControl: true,
        },
        timestamp: new Date(),
      },
    };

    expect(recording.id).toBe('test-id');
    expect(recording.language).toBe(SupportedLanguage.ENGLISH_US);
  });

  test('RecordingState type should be valid', () => {
    const state: RecordingState = {
      status: RecordingStatus.IDLE,
      transcript: '',
      interimTranscript: '',
      confidence: 0,
      duration: 0,
      audioLevel: 0,
      error: null,
      isSupported: true,
    };

    expect(state.status).toBe(RecordingStatus.IDLE);
    expect(state.isSupported).toBe(true);
  });

  test('AudioError type should be valid', () => {
    const error: AudioError = {
      code: AudioErrorCode.PERMISSION_DENIED,
      message: 'Microphone access denied',
      recoverable: true,
      timestamp: new Date(),
    };

    expect(error.code).toBe(AudioErrorCode.PERMISSION_DENIED);
    expect(error.recoverable).toBe(true);
  });

  test('BrowserCapabilities type should be valid', () => {
    const capabilities: BrowserCapabilities = {
      compatibilityScore: 85,
      supportedFeatures: [BrowserFeature.MEDIA_DEVICES, BrowserFeature.SPEECH_RECOGNITION],
      missingFeatures: [],
      warnings: [],
      browser: {
        name: 'chrome',
        version: '91',
        platform: 'MacIntel',
      },
    };

    expect(capabilities.compatibilityScore).toBe(85);
    expect(capabilities.supportedFeatures).toContain(BrowserFeature.MEDIA_DEVICES);
  });

  test('Enums should have correct values', () => {
    expect(SupportedLanguage.ENGLISH_US).toBe('en-US');
    expect(SupportedLanguage.HEBREW_IL).toBe('he-IL');
    
    expect(RecordingStatus.IDLE).toBe('idle');
    expect(RecordingStatus.RECORDING).toBe('recording');
    
    expect(AudioErrorCode.PERMISSION_DENIED).toBe('PERMISSION_DENIED');
    expect(AudioErrorCode.DEVICE_NOT_FOUND).toBe('DEVICE_NOT_FOUND');
  });
});
