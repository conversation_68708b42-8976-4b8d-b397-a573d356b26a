/**
 * Tests for the main page component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Home from '@/app/page';

// Mock the VoiceInput component
jest.mock('@/components/VoiceInput', () => {
  return {
    VoiceInput: ({ onRecordingComplete, onError, className }: any) => (
      <div data-testid="voice-input" className={className}>
        <button
          onClick={() => {
            // Simulate recording completion
            if (onRecordingComplete) {
              onRecordingComplete({
                id: 'test-recording-1',
                transcript: 'Test recording transcript',
                confidence: 0.95,
                audioBlob: null,
                duration: 5000,
                language: 'en-US',
                timestamp: new Date(),
                metadata: {
                  deviceType: 'desktop',
                  browserType: 'chrome',
                  browserVersion: '91',
                  audioQuality: { sampleRate: 44100, channels: 1 },
                  recordingEnvironment: {
                    hasNoiseCancellation: true,
                    hasEchoCancellation: true,
                    hasAutoGainControl: true,
                  },
                  timestamp: new Date(),
                },
              });
            }
          }}
        >
          Mock Record Button
        </button>
        <button
          onClick={() => {
            // Simulate recording error
            if (onError) {
              onError({
                code: 'PERMISSION_DENIED',
                message: 'Microphone access denied',
                recoverable: true,
                timestamp: new Date(),
              });
            }
          }}
        >
          Mock Error Button
        </button>
      </div>
    ),
  };
});

// Mock the AudioVisualizer component
jest.mock('@/components/AudioVisualizer', () => {
  return {
    AudioVisualizer: ({ audioLevel, isRecording }: any) => (
      <div data-testid="audio-visualizer">
        Audio Level: {audioLevel}, Recording: {isRecording.toString()}
      </div>
    ),
  };
});

// Mock window.matchMedia for responsive tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('Home Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial Render', () => {
    it('renders the main page without crashing', () => {
      const { container } = render(<Home />);
      // Check if anything renders at all
      expect(container.firstChild).toBeTruthy();
      // Look for any text content
      expect(container).toHaveTextContent('Coach Me');
    });

    it('displays the hero section', () => {
      render(<Home />);
      expect(screen.getByRole('heading', { name: 'Speak Your Thoughts' })).toBeInTheDocument();
      expect(screen.getByText(/Transform your voice into structured insights/)).toBeInTheDocument();
    });

    it('renders the voice input component', () => {
      render(<Home />);
      expect(screen.getByTestId('voice-input')).toBeInTheDocument();
    });

    it('shows keyboard shortcuts hint', () => {
      render(<Home />);
      expect(screen.getByText(/Space/)).toBeInTheDocument();
      expect(screen.getByText(/to record/)).toBeInTheDocument();
    });
  });

  describe('Language Selection', () => {
    it('renders language selector', () => {
      render(<Home />);
      const languageSelect = screen.getByLabelText('Select language');
      expect(languageSelect).toBeInTheDocument();
      expect(languageSelect).toHaveValue('en-US');
    });

    it('allows changing language', () => {
      render(<Home />);
      const languageSelect = screen.getByLabelText('Select language');
      
      fireEvent.change(languageSelect, { target: { value: 'he-IL' } });
      expect(languageSelect).toHaveValue('he-IL');
    });
  });

  describe('Recording History', () => {
    it('initially hides recording history', () => {
      render(<Home />);
      expect(screen.queryByText('Recent Recordings')).not.toBeInTheDocument();
    });

    it('shows recording history when history button is clicked', () => {
      render(<Home />);
      const historyButton = screen.getByLabelText('Toggle recording history');

      fireEvent.click(historyButton);
      expect(screen.getByRole('heading', { name: 'Recent Recordings' })).toBeInTheDocument();
    });

    it('shows empty state when no recordings exist', () => {
      render(<Home />);
      const historyButton = screen.getByLabelText('Toggle recording history');

      fireEvent.click(historyButton);
      expect(screen.getByText(/No recordings yet/)).toBeInTheDocument();
    });

    it('displays recordings after recording completion', async () => {
      render(<Home />);
      
      // Complete a recording
      const recordButton = screen.getByText('Mock Record Button');
      fireEvent.click(recordButton);
      
      // Open history
      const historyButton = screen.getByLabelText('Toggle recording history');
      fireEvent.click(historyButton);
      
      await waitFor(() => {
        expect(screen.getByText('Test recording transcript')).toBeInTheDocument();
        expect(screen.getByText('95% confidence')).toBeInTheDocument();
      });
    });

    it('limits recordings to 10 items', async () => {
      render(<Home />);
      
      // Add 12 recordings
      const recordButton = screen.getByText('Mock Record Button');
      for (let i = 0; i < 12; i++) {
        fireEvent.click(recordButton);
      }
      
      // Open history
      const historyButton = screen.getByLabelText('Toggle recording history');
      fireEvent.click(historyButton);
      
      await waitFor(() => {
        const recordings = screen.getAllByText('Test recording transcript');
        expect(recordings).toHaveLength(10);
      });
    });
  });

  describe('Help Section', () => {
    it('initially hides help section', () => {
      render(<Home />);
      expect(screen.queryByText('How to Use Coach Me')).not.toBeInTheDocument();
    });

    it('shows help section when help button is clicked', () => {
      render(<Home />);
      const helpButton = screen.getByLabelText('Toggle help');

      fireEvent.click(helpButton);
      expect(screen.getByRole('heading', { name: 'How to Use Coach Me' })).toBeInTheDocument();
    });

    it('displays help content', () => {
      render(<Home />);
      const helpButton = screen.getByLabelText('Toggle help');

      fireEvent.click(helpButton);
      expect(screen.getByRole('heading', { name: 'Getting Started' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Tips for Better Results' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Keyboard Shortcuts' })).toBeInTheDocument();
    });
  });

  describe('Keyboard Navigation', () => {
    it('closes panels when Escape is pressed', () => {
      render(<Home />);
      
      // Open help and history
      fireEvent.click(screen.getByLabelText('Toggle help'));
      fireEvent.click(screen.getByLabelText('Toggle recording history'));

      expect(screen.getByRole('heading', { name: 'How to Use Coach Me' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Recent Recordings' })).toBeInTheDocument();

      // Press Escape
      fireEvent.keyDown(document, { code: 'Escape' });

      expect(screen.queryByRole('heading', { name: 'How to Use Coach Me' })).not.toBeInTheDocument();
      expect(screen.queryByRole('heading', { name: 'Recent Recordings' })).not.toBeInTheDocument();
    });

    it('prevents default behavior for spacebar on body', () => {
      render(<Home />);
      
      const preventDefault = jest.fn();
      const event = new KeyboardEvent('keydown', { code: 'Space' });
      Object.defineProperty(event, 'preventDefault', { value: preventDefault });
      Object.defineProperty(event, 'target', { value: document.body });
      
      document.dispatchEvent(event);
      
      expect(preventDefault).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('handles recording errors gracefully', () => {
      render(<Home />);
      
      // Trigger an error
      const errorButton = screen.getByText('Mock Error Button');
      fireEvent.click(errorButton);
      
      // Error should be handled by VoiceInput component
      // No error should crash the main page
      expect(screen.getByRole('heading', { name: 'Coach Me' })).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('renders properly on mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<Home />);
      expect(screen.getByRole('heading', { name: 'Coach Me' })).toBeInTheDocument();
      expect(screen.getByTestId('voice-input')).toBeInTheDocument();
    });

    it('renders properly on desktop viewport', () => {
      // Mock desktop viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      });

      render(<Home />);
      expect(screen.getByRole('heading', { name: 'Coach Me' })).toBeInTheDocument();
      expect(screen.getByTestId('voice-input')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      render(<Home />);
      
      const h1 = screen.getByRole('heading', { level: 1 });
      const h2 = screen.getByRole('heading', { level: 2 });
      
      expect(h1).toHaveTextContent('Coach Me');
      expect(h2).toHaveTextContent('Speak Your Thoughts');
    });

    it('has proper ARIA labels for interactive elements', () => {
      render(<Home />);
      
      expect(screen.getByLabelText('Select language')).toBeInTheDocument();
      expect(screen.getByLabelText('Toggle recording history')).toBeInTheDocument();
      expect(screen.getByLabelText('Toggle help')).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      render(<Home />);
      
      const languageSelect = screen.getByLabelText('Select language');
      const historyButton = screen.getByLabelText('Toggle recording history');
      const helpButton = screen.getByLabelText('Toggle help');
      
      // All interactive elements should be focusable
      languageSelect.focus();
      expect(document.activeElement).toBe(languageSelect);
      
      historyButton.focus();
      expect(document.activeElement).toBe(historyButton);
      
      helpButton.focus();
      expect(document.activeElement).toBe(helpButton);
    });
  });
});
