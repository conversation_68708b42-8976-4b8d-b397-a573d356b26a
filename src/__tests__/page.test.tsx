/**
 * Tests for the main page component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Home from '@/app/page';

// Mock the VoiceInput component
jest.mock('@/components/VoiceInput', () => {
  return {
    VoiceInput: ({ onRecordingComplete, onError, className }: any) => (
      <div data-testid="voice-input" className={className}>
        <button
          data-testid="mock-record-button"
          onClick={() => {
            // Simulate recording completion
            if (onRecordingComplete) {
              onRecordingComplete({
                id: 'test-recording-1',
                transcript: 'Test recording transcript',
                confidence: 0.95,
                audioBlob: null,
                duration: 5000,
                language: 'en-US',
                timestamp: new Date(),
                metadata: {
                  deviceType: 'desktop',
                  browserType: 'chrome',
                  browserVersion: '91',
                  audioQuality: { sampleRate: 44100, channels: 1 },
                  recordingEnvironment: {
                    hasNoiseCancellation: true,
                    hasEchoCancellation: true,
                    hasAutoGainControl: true,
                  },
                  timestamp: new Date(),
                },
              });
            }
          }}
        >
          Mock Record Button
        </button>
        <button
          data-testid="mock-error-button"
          onClick={() => {
            // Simulate recording error
            if (onError) {
              onError({
                code: 'PERMISSION_DENIED',
                message: 'Microphone access denied',
                recoverable: true,
                timestamp: new Date(),
              });
            }
          }}
        >
          Mock Error Button
        </button>
      </div>
    ),
  };
});

// Mock the AudioVisualizer component
jest.mock('@/components/AudioVisualizer', () => {
  return {
    AudioVisualizer: ({ audioLevel, isRecording }: any) => (
      <div data-testid="audio-visualizer">
        Audio Level: {audioLevel}, Recording: {isRecording.toString()}
      </div>
    ),
  };
});

// Mock window.matchMedia for responsive tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('Home Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset any DOM state
    document.body.innerHTML = '';
  });

  describe('Initial Render', () => {
    it('renders the main page without crashing', () => {
      const { container } = render(<Home />);

      // Check if anything renders at all
      expect(container.firstChild).toBeTruthy();

      // Look for any text content
      expect(container).toHaveTextContent('Coach Me');
    });

    it('displays the hero section', () => {
      const { container } = render(<Home />);
      expect(container).toHaveTextContent('Speak Your Thoughts');
      expect(container).toHaveTextContent('Transform your voice into structured insights');
    });

    it('renders the voice input component', () => {
      const { container } = render(<Home />);
      const voiceInput = container.querySelector('[data-testid="voice-input"]');
      expect(voiceInput).toBeTruthy();
    });

    it('shows keyboard shortcuts hint', () => {
      const { container } = render(<Home />);
      expect(container).toHaveTextContent('Space');
      expect(container).toHaveTextContent('to record');
    });
  });

  describe('Language Selection', () => {
    it('renders language selector', () => {
      const { container } = render(<Home />);
      const languageSelect = container.querySelector('select[aria-label="Select language"]');
      expect(languageSelect).toBeTruthy();
      expect(languageSelect).toHaveValue('en-US');
    });

    it('allows changing language', () => {
      const { container } = render(<Home />);
      const languageSelect = container.querySelector('select[aria-label="Select language"]');

      fireEvent.change(languageSelect!, { target: { value: 'he-IL' } });
      expect(languageSelect).toHaveValue('he-IL');
    });
  });

  describe('Recording History', () => {
    it('initially hides recording history', () => {
      const { container } = render(<Home />);
      expect(container).not.toHaveTextContent('Recent Recordings');
    });

    it('shows recording history when history button is clicked', () => {
      const { container } = render(<Home />);
      const historyButton = container.querySelector('button[aria-label="Toggle recording history"]');

      fireEvent.click(historyButton!);
      expect(container).toHaveTextContent('Recent Recordings');
    });

    it('shows empty state when no recordings exist', () => {
      const { container } = render(<Home />);
      const historyButton = container.querySelector('button[aria-label="Toggle recording history"]');

      fireEvent.click(historyButton!);
      expect(container).toHaveTextContent('No recordings yet');
    });

    it('displays recordings after recording completion', async () => {
      const { container } = render(<Home />);

      // Complete a recording
      const recordButton = container.querySelector('[data-testid="mock-record-button"]');
      fireEvent.click(recordButton!);

      // Open history
      const historyButton = container.querySelector('button[aria-label="Toggle recording history"]');
      fireEvent.click(historyButton!);

      await waitFor(() => {
        expect(container).toHaveTextContent('Test recording transcript');
        expect(container).toHaveTextContent('95% confidence');
      });
    });

    it('limits recordings to 10 items', async () => {
      const { container } = render(<Home />);

      // Add 12 recordings
      const recordButton = container.querySelector('[data-testid="mock-record-button"]');
      for (let i = 0; i < 12; i++) {
        fireEvent.click(recordButton!);
      }

      // Open history
      const historyButton = container.querySelector('button[aria-label="Toggle recording history"]');
      fireEvent.click(historyButton!);

      await waitFor(() => {
        // Check the text content for the number of recordings
        const textContent = container.textContent || '';
        const matches = (textContent.match(/Test recording transcript/g) || []).length;
        expect(matches).toBe(10);
      });
    });
  });

  describe('Help Section', () => {
    it('initially hides help section', () => {
      const { container } = render(<Home />);
      expect(container).not.toHaveTextContent('How to Use Coach Me');
    });

    it('shows help section when help button is clicked', () => {
      const { container } = render(<Home />);
      const helpButton = container.querySelector('button[aria-label="Toggle help"]');

      fireEvent.click(helpButton!);
      expect(container).toHaveTextContent('How to Use Coach Me');
    });

    it('displays help content', () => {
      const { container } = render(<Home />);
      const helpButton = container.querySelector('button[aria-label="Toggle help"]');

      fireEvent.click(helpButton!);
      expect(container).toHaveTextContent('Getting Started');
      expect(container).toHaveTextContent('Tips for Better Results');
      expect(container).toHaveTextContent('Keyboard Shortcuts');
    });
  });

  describe('Keyboard Navigation', () => {
    it('closes panels when Escape is pressed', () => {
      const { container } = render(<Home />);

      // Open help and history
      const helpButton = container.querySelector('button[aria-label="Toggle help"]');
      const historyButton = container.querySelector('button[aria-label="Toggle recording history"]');

      fireEvent.click(helpButton!);
      fireEvent.click(historyButton!);

      expect(container).toHaveTextContent('How to Use Coach Me');
      expect(container).toHaveTextContent('Recent Recordings');

      // Press Escape
      fireEvent.keyDown(document, { code: 'Escape' });

      expect(container).not.toHaveTextContent('How to Use Coach Me');
      expect(container).not.toHaveTextContent('Recent Recordings');
    });

    it('prevents default behavior for spacebar on body', () => {
      render(<Home />);

      const preventDefault = jest.fn();
      const event = new KeyboardEvent('keydown', { code: 'Space' });
      Object.defineProperty(event, 'preventDefault', { value: preventDefault });
      Object.defineProperty(event, 'target', { value: document.body });

      document.dispatchEvent(event);

      expect(preventDefault).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('handles recording errors gracefully', () => {
      const { container } = render(<Home />);

      // Trigger an error
      const errorButton = container.querySelector('[data-testid="mock-error-button"]');
      fireEvent.click(errorButton!);

      // Error should be handled by VoiceInput component
      // No error should crash the main page
      expect(container).toHaveTextContent('Coach Me');
    });
  });

  describe('Responsive Design', () => {
    it('renders properly on mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { container } = render(<Home />);
      expect(container).toHaveTextContent('Coach Me');
      expect(container.querySelector('[data-testid="voice-input"]')).toBeTruthy();
    });

    it('renders properly on desktop viewport', () => {
      // Mock desktop viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      });

      const { container } = render(<Home />);
      expect(container).toHaveTextContent('Coach Me');
      expect(container.querySelector('[data-testid="voice-input"]')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading hierarchy', () => {
      const { container } = render(<Home />);

      // Check that headings exist with proper content
      expect(container).toHaveTextContent('Coach Me');
      expect(container).toHaveTextContent('Speak Your Thoughts');
    });

    it('has proper ARIA labels for interactive elements', () => {
      const { container } = render(<Home />);

      expect(container.querySelector('[aria-label="Select language"]')).toBeTruthy();
      expect(container.querySelector('[aria-label="Toggle recording history"]')).toBeTruthy();
      expect(container.querySelector('[aria-label="Toggle help"]')).toBeTruthy();
    });

    it('supports keyboard navigation', () => {
      const { container } = render(<Home />);

      const languageSelect = container.querySelector('[aria-label="Select language"]') as HTMLElement;
      const historyButton = container.querySelector('[aria-label="Toggle recording history"]') as HTMLElement;
      const helpButton = container.querySelector('[aria-label="Toggle help"]') as HTMLElement;

      // Check that elements exist and are focusable
      expect(languageSelect).toBeTruthy();
      expect(historyButton).toBeTruthy();
      expect(helpButton).toBeTruthy();

      // Test that elements have the correct attributes for accessibility
      expect(languageSelect?.getAttribute('aria-label')).toBe('Select language');
      expect(historyButton?.getAttribute('aria-label')).toBe('Toggle recording history');
      expect(helpButton?.getAttribute('aria-label')).toBe('Toggle help');
    });
  });
});
