/**
 * Jest setup file for Coach Me application
 * Configures testing environment and global mocks
 */

// Import testing library extensions
import '@testing-library/jest-dom';

// Custom matchers for better assertions
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});

// Global test utilities
global.testUtils = {
  // Helper to create mock audio recording
  createMockRecording: (overrides = {}) => ({
    id: 'test-recording-' + Math.random().toString(36).substr(2, 9),
    transcript: 'Test recording transcript',
    confidence: 0.95,
    audioBlob: null,
    duration: 5000,
    language: 'en-US',
    timestamp: new Date(),
    metadata: {
      deviceType: 'desktop',
      browserType: 'chrome',
      browserVersion: '91',
      audioQuality: { sampleRate: 44100, channels: 1 },
      recordingEnvironment: {
        hasNoiseCancellation: true,
        hasEchoCancellation: true,
        hasAutoGainControl: true,
      },
      timestamp: new Date(),
    },
    ...overrides,
  }),

  // Helper to create mock error
  createMockError: (overrides = {}) => ({
    code: 'PERMISSION_DENIED',
    message: 'Microphone access denied',
    recoverable: true,
    timestamp: new Date(),
    ...overrides,
  }),

  // Helper to wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to trigger keyboard events
  triggerKeyboardEvent: (element, eventType, keyCode) => {
    const event = new KeyboardEvent(eventType, {
      code: keyCode,
      key: keyCode,
      bubbles: true,
      cancelable: true,
    });
    element.dispatchEvent(event);
    return event;
  },
};

// Console error suppression for expected errors in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
       args[0].includes('Warning: An invalid form control'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset DOM
  document.body.innerHTML = '';

  // Reset any global state
  if (global.testState) {
    global.testState = {};
  }
});

// Performance monitoring for tests
const performanceThreshold = 1000; // 1 second

beforeEach(() => {
  global.testStartTime = performance.now();
});

afterEach(() => {
  const testDuration = performance.now() - global.testStartTime;
  if (testDuration > performanceThreshold) {
    console.warn(
      `Test took ${testDuration.toFixed(2)}ms, which exceeds the ${performanceThreshold}ms threshold`
    );
  }
});
