// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Mock Web APIs that aren't available in Jest environment
global.MediaRecorder = jest.fn(() => ({
  start: jest.fn(),
  stop: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
}));

global.navigator.mediaDevices = {
  getUserMedia: jest.fn(),
};

global.AudioContext = jest.fn(() => ({
  createAnalyser: jest.fn(),
  createMediaStreamSource: jest.fn(),
  close: jest.fn(),
}));

global.URL.createObjectURL = jest.fn();
