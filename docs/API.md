# API Documentation

## Components

### VoiceInput

The main voice recording component that handles audio capture, speech recognition, and user interaction.

#### Props

```typescript
interface VoiceInputProps {
  /** Callback fired when recording is completed */
  onRecordingComplete: (recording: AudioRecording) => void;
  
  /** Callback fired when an error occurs */
  onError: (error: AudioError) => void;
  
  /** Language for speech recognition (optional) */
  language?: SupportedLanguage;
  
  /** Maximum recording duration in milliseconds (optional) */
  maxDuration?: number;
  
  /** Whether the component is disabled (optional) */
  disabled?: boolean;
  
  /** Whether to show real-time transcript (optional, default: true) */
  showTranscript?: boolean;
  
  /** Whether to show audio level visualization (optional, default: true) */
  showAudioLevel?: boolean;
  
  /** Additional CSS classes (optional) */
  className?: string;
}
```

#### Usage Example

```tsx
import { VoiceInput } from '@/components/VoiceInput';
import { AudioRecording, AudioError, SupportedLanguage } from '@/lib/types';

function MyComponent() {
  const handleRecordingComplete = (recording: AudioRecording) => {
    console.log('Recording completed:', recording.transcript);
    console.log('Confidence:', recording.confidence);
  };

  const handleError = (error: AudioError) => {
    console.error('Recording error:', error.message);
    if (error.recoverable) {
      // Show retry option to user
    }
  };

  return (
    <VoiceInput
      onRecordingComplete={handleRecordingComplete}
      onError={handleError}
      language={SupportedLanguage.ENGLISH_US}
      maxDuration={300000} // 5 minutes
      showTranscript={true}
      showAudioLevel={true}
    />
  );
}
```

#### Accessibility Features

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Space to start/stop, Enter as alternative, Escape to cancel
- **Visual Feedback**: High contrast states, clear error messages
- **Reduced Motion**: Respects user preferences

### AudioVisualizer

Real-time audio level visualization component.

#### Props

```typescript
interface AudioVisualizerProps {
  /** Audio level (0-1) from the recording hook */
  audioLevel: number;
  
  /** Whether recording is active */
  isRecording: boolean;
  
  /** Visualization type (optional, default: 'circular') */
  type?: 'circular' | 'waveform' | 'pulse';
  
  /** Size of the visualization (optional, default: 'medium') */
  size?: 'small' | 'medium' | 'large';
  
  /** Color theme (optional, default: 'voice') */
  theme?: 'primary' | 'voice' | 'accent';
  
  /** Additional CSS classes (optional) */
  className?: string;
  
  /** Whether to respect reduced motion preferences (optional, default: true) */
  respectReducedMotion?: boolean;
}
```

#### Usage Example

```tsx
import { AudioVisualizer } from '@/components/AudioVisualizer';

function MyComponent() {
  const { audioLevel, isRecording } = useAudioRecording();

  return (
    <AudioVisualizer
      audioLevel={audioLevel}
      isRecording={isRecording}
      type="circular"
      size="large"
      theme="voice"
    />
  );
}
```

## Hooks

### useAudioRecording

Core hook for audio recording functionality.

#### Return Value

```typescript
interface AudioRecordingState {
  // State
  isRecording: boolean;
  transcript: string;
  error: AudioError | null;
  duration: number;
  audioLevel: number;
  status: RecordingStatus;
  
  // Actions
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  clearError: () => void;
  reset: () => void;
  
  // Data
  audioBlob: Blob | null;
  finalTranscript: string;
  confidence: number;
}
```

#### Usage Example

```tsx
import { useAudioRecording } from '@/hooks/useAudioRecording';
import { SupportedLanguage } from '@/lib/types';

function MyComponent() {
  const {
    isRecording,
    transcript,
    error,
    duration,
    audioLevel,
    status,
    startRecording,
    stopRecording,
    clearError,
    reset,
    audioBlob,
    finalTranscript,
    confidence
  } = useAudioRecording({
    language: SupportedLanguage.ENGLISH_US,
    maxDuration: 300000,
    onRecordingComplete: (recording) => {
      console.log('Recording completed:', recording);
    },
    onError: (error) => {
      console.error('Recording error:', error);
    }
  });

  return (
    <div>
      <button onClick={isRecording ? stopRecording : startRecording}>
        {isRecording ? 'Stop' : 'Start'} Recording
      </button>
      {transcript && <p>Transcript: {transcript}</p>}
      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

#### Configuration Options

```typescript
interface UseAudioRecordingOptions {
  /** Language for speech recognition */
  language?: SupportedLanguage;
  
  /** Maximum recording duration in milliseconds */
  maxDuration?: number;
  
  /** Callback fired when recording is completed */
  onRecordingComplete?: (recording: AudioRecording) => void;
  
  /** Callback fired when an error occurs */
  onError?: (error: AudioError) => void;
  
  /** Whether to enable real-time transcription */
  enableTranscription?: boolean;
  
  /** Whether to enable audio level monitoring */
  enableAudioLevel?: boolean;
}
```

## Utilities

### Browser Compatibility

Functions for detecting browser capabilities and handling compatibility issues.

#### detectBrowser()

```typescript
function detectBrowser(): BrowserInfo;

interface BrowserInfo {
  name: 'chrome' | 'firefox' | 'safari' | 'edge' | 'unknown';
  version: string;
  platform: string;
  isMobile: boolean;
}
```

#### isFeatureSupported()

```typescript
function isFeatureSupported(feature: BrowserFeature): boolean;

enum BrowserFeature {
  MEDIA_DEVICES = 'mediaDevices',
  SPEECH_RECOGNITION = 'speechRecognition',
  MEDIA_RECORDER = 'mediaRecorder',
  AUDIO_CONTEXT = 'audioContext',
  BLOB_SUPPORT = 'blobSupport',
  MICROPHONE_ACCESS = 'microphoneAccess'
}
```

#### assessBrowserCapabilities()

```typescript
function assessBrowserCapabilities(): Promise<CapabilityAssessment>;

interface CapabilityAssessment {
  compatibilityScore: number; // 0-100
  supportedFeatures: BrowserFeature[];
  missingFeatures: MissingFeature[];
  warnings: string[];
  browser: BrowserInfo;
}
```

#### Usage Example

```tsx
import { 
  detectBrowser, 
  isFeatureSupported, 
  assessBrowserCapabilities,
  BrowserFeature 
} from '@/lib/browserCompat';

async function checkCompatibility() {
  const browser = detectBrowser();
  console.log(`Browser: ${browser.name} ${browser.version}`);

  const hasMediaDevices = isFeatureSupported(BrowserFeature.MEDIA_DEVICES);
  const hasSpeechRecognition = isFeatureSupported(BrowserFeature.SPEECH_RECOGNITION);

  const assessment = await assessBrowserCapabilities();
  console.log(`Compatibility score: ${assessment.compatibilityScore}%`);
  
  if (assessment.compatibilityScore < 70) {
    // Show upgrade message or fallback UI
  }
}
```

### Permission Management

#### checkMicrophonePermission()

```typescript
function checkMicrophonePermission(): Promise<PermissionState>;

enum PermissionState {
  GRANTED = 'granted',
  DENIED = 'denied',
  PROMPT = 'prompt',
  UNKNOWN = 'unknown'
}
```

#### requestMicrophonePermission()

```typescript
function requestMicrophonePermission(): Promise<PermissionResult>;

interface PermissionResult {
  success: boolean;
  data?: MediaStream;
  error?: AudioError;
}
```

## Types

### Core Types

#### AudioRecording

```typescript
interface AudioRecording {
  id: string; // UUID
  transcript: string;
  confidence: number; // 0-1
  audioBlob: Blob | null;
  duration: number; // milliseconds
  language: SupportedLanguage;
  timestamp: Date;
  metadata: RecordingMetadata;
}
```

#### AudioError

```typescript
interface AudioError {
  code: ErrorCode;
  message: string;
  details?: string;
  recoverable: boolean;
  timestamp: Date;
}

enum ErrorCode {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  RECORDING_FAILED = 'RECORDING_FAILED',
  TRANSCRIPTION_FAILED = 'TRANSCRIPTION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNSUPPORTED_BROWSER = 'UNSUPPORTED_BROWSER'
}
```

#### RecordingMetadata

```typescript
interface RecordingMetadata {
  deviceType: 'mobile' | 'desktop' | 'tablet';
  browserType: string;
  browserVersion: string;
  audioQuality: AudioQualityMetrics;
  recordingEnvironment: RecordingEnvironment;
  timestamp: Date;
}

interface AudioQualityMetrics {
  sampleRate: number;
  channels: number;
  bitrate?: number;
}

interface RecordingEnvironment {
  hasNoiseCancellation: boolean;
  hasEchoCancellation: boolean;
  hasAutoGainControl: boolean;
}
```

### State Types

#### RecordingStatus

```typescript
enum RecordingStatus {
  IDLE = 'idle',
  REQUESTING_PERMISSION = 'requesting_permission',
  RECORDING = 'recording',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error'
}
```

#### SupportedLanguage

```typescript
enum SupportedLanguage {
  ENGLISH_US = 'en-US',
  HEBREW_IL = 'he-IL'
}
```

## Error Handling

### Error Recovery Strategies

1. **Permission Errors**: Guide user through browser permission setup
2. **Device Errors**: Retry with different audio constraints
3. **Network Errors**: Offline mode or retry logic
4. **Browser Compatibility**: Graceful degradation with fallbacks

### Error Display Guidelines

- Use clear, actionable error messages
- Provide recovery instructions when possible
- Show retry buttons for recoverable errors
- Include help links for complex issues

### Example Error Handling

```tsx
function handleRecordingError(error: AudioError) {
  switch (error.code) {
    case ErrorCode.PERMISSION_DENIED:
      showPermissionInstructions();
      break;
    case ErrorCode.DEVICE_NOT_FOUND:
      showDeviceSetupHelp();
      break;
    case ErrorCode.UNSUPPORTED_BROWSER:
      showBrowserUpgradeMessage();
      break;
    default:
      showGenericErrorMessage(error.message);
  }
}
```
