# Coach Me - AI Agent Development Tasks (CTO-Level Detail)

## 🎯 Project Overview & Context

**Product Vision**: Transform spontaneous voice thoughts into structured, emotionally-grounded coaching prompts that drive behavioral change through self-awareness and scheduled reinforcement.

**Business Context**: This is a minimum viable product (MVP) targeting the $4.4B personal development app market, with unique differentiation through voice-first interaction and emotional motivation capture.

**Technical Philosophy**: Domain-driven design with event-sourcing, clean architecture, and maximum testability. Every component must be independently deployable and scalable to 100k+ users.

---

## PHASE 1: Voice Recording Infrastructure Foundation 🎤

### 📋 Phase 1 Overview
**Objective**: Establish bulletproof voice recording infrastructure with production-grade error handling, accessibility, and performance optimization.

**Success Metrics**: 
- <500ms UI response time
- >95% browser compatibility
- Zero audio dropouts during recording
- Graceful degradation on unsupported browsers

---

### Task 1.1: Project Architecture & Setup
**Priority**: P0 (Blocking) | **Estimated Time**: 4-6 hours

#### Context & Requirements
You're building the foundation of a voice-first application that will scale to handle thousands of concurrent voice recordings. The architecture must support real-time audio processing, multi-language support, and future AI integration.

#### Detailed Instructions

**Step 1: Initialize Next.js Project**
- Use Next.js 14 with App Router (not Pages Router) for better performance and developer experience
- Enable TypeScript from the start - this is non-negotiable for maintainability
- Configure Tailwind CSS for rapid UI development with consistent design system
- Set up ESLint with strict rules to catch errors early
- Use src directory structure for better organization
- Configure import aliases (@/*) to avoid relative import hell

**Step 2: Install Core Dependencies**
You need these specific packages:
- **zustand**: For lightweight, type-safe state management (avoid Redux complexity)
- **zod**: For runtime type validation and API contract enforcement
- **clsx**: For conditional CSS class management
- **lucide-react**: For consistent, lightweight icons
- Ensure all packages are latest stable versions to avoid security vulnerabilities

**Step 3: Project Structure Setup**
Create a scalable folder structure that follows domain-driven design principles:
- `components/ui/` - Only pure, reusable UI components with no business logic
- `lib/` - Pure utility functions, types, and constants
- `hooks/` - Custom React hooks for reusable stateful logic
- `stores/` - Zustand stores for global state management
- `__tests__/` - Co-located tests following the same structure as src

**Step 4: Configuration Files**
- **tailwind.config.js**: Extend default theme with brand colors, custom animations for voice feedback
- **next.config.js**: Enable experimental features like typed routes, optimize images, remove console logs in production
- **tsconfig.json**: Strict TypeScript settings with path mapping
- **.eslintrc.json**: Extend Next.js ESLint config with additional rules for code quality
- **prettier.config.js**: Consistent code formatting with Tailwind class sorting

#### Acceptance Criteria
- [ ] Project initializes without errors or warnings
- [ ] All dependencies install with compatible versions (no peer dependency conflicts)
- [ ] TypeScript compilation works without any errors
- [ ] Tailwind CSS compiles and hot-reloads properly
- [ ] Development server starts on localhost:3000 and displays default page
- [ ] ESLint shows no errors on initial codebase
- [ ] Prettier formats code correctly with Tailwind class ordering

#### Quality Gates
- Run `npm run build` - must complete without errors
- Run `npm run lint` - must show zero errors
- Check bundle size - initial bundle should be <200KB
- Verify hot reload works in under 200ms

---

### Task 1.2: Core Type Definitions & Domain Models
**Priority**: P0 (Blocking) | **Estimated Time**: 3-4 hours

#### Context & Requirements
Establish bulletproof TypeScript contracts that will serve as the single source of truth for all audio operations. These types must be designed for extension - they'll support multi-language processing, AI integration, and future coaching features. Think of these as your API contracts that will never break.

#### Detailed Instructions

**Step 1: Core Domain Types (src/lib/types.ts)**
Define the fundamental domain objects that represent your business logic:

**AudioRecording Interface Design:**
- `id`: Use UUIDs, not auto-incrementing numbers (better for distributed systems)
- `transcript`: The raw text from speech recognition
- `confidence`: Float 0-1 representing speech recognition confidence (critical for UX decisions)
- `audioBlob`: Nullable because we might process without storing audio
- `duration`: Milliseconds (not seconds) for precision
- `language`: Enum type for supported languages (en-US, he-IL initially)
- `timestamp`: Use Date objects (JSON serializable)
- `metadata`: Separate object for recording context (device, browser, quality metrics)

**RecordingMetadata Design Principles:**
- Capture device type for different UX flows (mobile vs desktop)
- Browser type for troubleshooting compatibility issues
- Audio quality metrics for debugging poor transcription
- Technical specs (sample rate, channels) for audio processing optimization

**State Management Types:**
- `RecordingState`: Represents the current state of the recording process
- Use discriminated unions for `RecordingStatus` (TypeScript will enforce valid state transitions)
- `AudioError`: Structured error handling with error codes, user messages, and recovery flags
- Include confidence scores and audio levels for real-time UI feedback

**Step 2: Error Handling Strategy (src/lib/types.ts continued)**
Design a robust error system that handles all failure modes:

**Error Code Categories:**
- Permission errors (PERMISSION_DENIED, DEVICE_NOT_FOUND)
- Technical errors (RECORDING_FAILED, TRANSCRIPTION_FAILED)
- Network errors (NETWORK_ERROR)
- Compatibility errors (UNSUPPORTED_BROWSER)

**Error Object Design:**
- `code`: Enum for programmatic handling
- `message`: User-friendly message for display
- `details`: Technical details for debugging (don't show to users)
- `recoverable`: Boolean flag to determine if user can retry

**Step 3: Configuration Types**
Define all configuration objects as immutable constants:
- Audio processing settings (sample rate, noise cancellation)
- Recording limits (max duration, file size)
- Language configuration with RTL support flags
- Browser compatibility matrix

#### Acceptance Criteria
- [ ] All types compile without TypeScript errors
- [ ] Types support both English and Hebrew languages
- [ ] Error types cover all possible failure scenarios
- [ ] Configuration types are immutable and type-safe
- [ ] Types are exported correctly and can be imported in other files
- [ ] JSDoc comments explain complex types
- [ ] Types follow consistent naming conventions (PascalCase for interfaces, camelCase for properties)

#### Quality Gates
- Create a test file that imports all types - must compile
- Types should enable autocomplete in VSCode
- No `any` types allowed (use `unknown` if truly dynamic)
- All types should be serializable to JSON

---

### Task 1.3: Browser Compatibility & Feature Detection
**Priority**: P0 (Blocking) | **Estimated Time**: 2-3 hours

#### Context & Requirements
Voice recording depends on browser APIs that have varying support and behavior. You must detect capabilities early and provide graceful fallbacks. This system will determine if users can use the app at all.

#### Detailed Instructions

**Step 1: Feature Detection Utility (src/lib/browserCompat.ts)**
Create a comprehensive browser capability detection system:

**Core Capabilities to Check:**
- `navigator.mediaDevices.getUserMedia` - Modern media access
- `webkitSpeechRecognition` or `SpeechRecognition` - Speech recognition API
- `MediaRecorder` - Audio recording capability
- `AudioContext` - Audio processing
- `Blob` and `URL.createObjectURL` - File handling

**Browser-Specific Checks:**
- Safari: Different permission model, requires user gesture for microphone access
- Firefox: Limited speech recognition support
- Chrome: Full feature support but may have different audio formats
- Mobile browsers: Touch event requirements, different audio constraints

**Step 2: Capability Assessment Function**
Design a function that returns a capability profile:
- Overall compatibility score (0-100)
- List of supported features
- List of missing features with fallback suggestions
- Performance warnings (e.g., "Safari on iOS has limited background processing")

**Step 3: Graceful Degradation Strategy**
Define fallback behaviors for each missing capability:
- No speech recognition: Show text input alternative
- No audio recording: Allow text-only mode
- No microphone access: Clear instructions for enabling permissions
- Old browser: Show upgrade message with download links

**Step 4: Permission Handling**
Create robust permission request flow:
- Check current permission state before requesting
- Handle all permission states (granted, denied, prompt)
- Provide clear instructions for re-enabling permissions
- Different flows for different browsers (Safari requires different approach)

#### Acceptance Criteria
- [ ] Function correctly identifies browser type and version
- [ ] All required Web APIs are properly detected
- [ ] Permission states are correctly identified
- [ ] Fallback recommendations are provided for each missing feature
- [ ] Function works on iOS Safari, Chrome Mobile, Chrome Desktop, Firefox
- [ ] Returns consistent results across browser sessions
- [ ] Handles edge cases like incognito mode or restricted environments

#### Quality Gates
- Test on minimum 5 different browser/OS combinations
- Function should never throw exceptions (only return capability data)
- Performance: Feature detection should complete in <100ms
- Results should be cached to avoid repeated capability checks

---

### Task 1.4: Audio Recording Hook Implementation
**Priority**: P0 (Blocking) | **Estimated Time**: 6-8 hours

#### Context & Requirements
This is the core of your voice recording system. The hook must handle the complex state machine of audio recording: permission requests, device initialization, recording state management, error handling, and cleanup. This hook will be reused across the entire application.

#### Detailed Instructions

**Step 1: Hook Architecture Design (src/hooks/useAudioRecording.ts)**
Design a custom hook that encapsulates all recording logic:

**State Management Strategy:**
- Use `useReducer` instead of multiple `useState` calls for complex state
- State should include: recording status, transcript, audio blob, error state, duration, audio levels
- Implement proper state transitions (idle → requesting permission → recording → processing → completed)
- Add state validation to prevent invalid transitions

**Step 2: MediaDevices Integration**
Handle the Web Audio API integration:

**getUserMedia Configuration:**
- Request audio with specific constraints (echo cancellation, noise suppression)
- Handle different browser implementations of audio constraints
- Implement retry logic with different constraint sets if first attempt fails
- Store MediaStream reference for proper cleanup

**MediaRecorder Setup:**
- Choose optimal audio format based on browser support (webm, mp4, wav)
- Configure recording options (bitrate, sample rate)
- Handle dataavailable events to collect audio chunks
- Implement proper start/stop/pause functionality

**Step 3: Speech Recognition Integration**
Implement real-time transcription:

**SpeechRecognition Configuration:**
- Use webkitSpeechRecognition with fallback to SpeechRecognition
- Configure for continuous recognition with interim results
- Set language based on user preference or browser locale
- Handle recognition errors and restart logic

**Real-time Transcript Processing:**
- Update transcript as interim results come in
- Finalize transcript on result events
- Handle multiple recognition languages
- Store confidence scores for quality assessment

**Step 4: Error Handling & Recovery**
Implement comprehensive error management:

**Error Categories:**
- Permission errors: Clear instructions for user action
- Device errors: Retry with different audio constraints
- Network errors: Offline mode or retry logic
- Browser compatibility: Graceful degradation

**Recovery Strategies:**
- Automatic retry for transient errors
- User-initiated retry for permission issues
- Clear error messages with actionable instructions
- Cleanup resources on any error

**Step 5: Performance Optimization**
Ensure the hook performs well under load:

**Memory Management:**
- Properly dispose of MediaStream objects
- Clean up event listeners
- Release audio blobs when no longer needed
- Implement cleanup in useEffect return function

**Audio Level Monitoring:**
- Use AudioContext and AnalyserNode for real-time volume detection
- Implement efficient audio level calculation (not every frame)
- Provide audio levels for UI visualization
- Handle audio processing in separate thread if possible

#### Hook API Design
The hook should return an object with:
- State: `{ isRecording, transcript, error, duration, audioLevel, status }`
- Actions: `{ startRecording, stopRecording, clearError, reset }`
- Data: `{ audioBlob, finalTranscript, confidence }`

#### Acceptance Criteria
- [ ] Hook handles all recording states correctly
- [ ] Real-time transcription works in both English and Hebrew
- [ ] Audio levels are provided for UI visualization
- [ ] All error scenarios are handled gracefully
- [ ] Resources are properly cleaned up on unmount
- [ ] Hook works on mobile and desktop browsers
- [ ] Recording can be started/stopped multiple times without issues
- [ ] Audio quality is suitable for speech recognition

#### Quality Gates
- No memory leaks after multiple record/stop cycles
- Recording starts within 500ms of user action
- Transcription accuracy >80% for clear speech
- Hook should work offline (no transcription but audio recording works)
- Error states should recover automatically when possible

---

### Task 1.5: Voice Input UI Component
**Priority**: P1 (High) | **Estimated Time**: 4-5 hours

#### Context & Requirements
Create a beautiful, accessible voice input component that provides clear visual feedback for all recording states. This component must work flawlessly on mobile devices and provide an intuitive user experience that encourages voice interaction.

#### Detailed Instructions

**Step 1: Component Architecture (src/components/VoiceInput.tsx)**
Design a pure, reusable component:

**Props Interface:**
- `onRecordingComplete`: Callback with AudioRecording object
- `onError`: Error callback with structured error information
- `language`: Optional language override
- `maxDuration`: Optional recording time limit
- `disabled`: For form integration
- `className`: For styling flexibility

**Component State Strategy:**
- Use the custom hook from Task 1.4 for all recording logic
- Component state should only manage UI-specific concerns (animations, visual feedback)
- No business logic in the component - it's purely presentational

**Step 2: Visual Design System**
Create engaging visual feedback:

**Recording Button States:**
- Idle: Large, prominent microphone icon with subtle shadow
- Recording: Pulsing animation with red indicator
- Processing: Spinner with "processing" message
- Error: Red state with error icon
- Completed: Green checkmark with success state

**Animation Strategy:**
- Use CSS transforms (not JavaScript animations) for performance
- Implement